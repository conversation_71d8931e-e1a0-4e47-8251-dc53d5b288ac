{"name": "emails-service", "version": "1.2.3", "description": "", "author": "Денис Благовещенский", "private": true, "license": "UNLICENSED", "scripts": {"preinstall": "only-allow pnpm", "build": "npx nest build && npm run copy-templates", "format": "prettier --write \"src/**/*.ts\" \"tests/**/*.ts\"", "lint": "eslint \"{src,tests}/**/*.ts\" --fix", "infra:start": "docker-compose -f 'node_modules/@skillspace/lib/infrastructure/docker-compose.infra.yaml' up -d", "infra:stop": "docker-compose -f 'node_modules/@skillspace/lib/infrastructure/docker-compose.infra.yaml' down", "start": "npm run build && ts-node src/main.ts", "start:dev": "npm run build && ts-node-dev --respawn --transpile-only src/main.ts", "start:debug": "npm run build && ts-node --inspect src/main.ts", "start:prod": "node dist/main", "prisma:gen": "prisma generate", "db:push": "prisma db push", "seed:run": "ts-node prisma/seed.ts", "pretest": "npx prisma generate", "test": "jest --config='tests/jest.config.js' --runInBand -b", "test:watch": "jest --config='tests/jest.config.js' --runInBand -b --watch", "test:coverage": "jest --config='tests/jest.config.js' --runInBand -b --coverage=true", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "copy-templates": "copyfiles -u 1 src/**/*.twig dist/"}, "dependencies": {"@apollo/server": "^4.11.3", "@apollo/subgraph": "2.2.3", "@nestjs/apollo": "^13.0.3", "@nestjs/common": "^11.0.10", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.10", "@nestjs/cqrs": "^11.0.2", "@nestjs/event-emitter": "^3.0.1", "@nestjs/graphql": "^13.0.3", "@nestjs/platform-express": "^11.0.10", "@nestjs/terminus": "^11.0.0", "@prisma/client": "^6.4.1", "@skillspace/amqp-contracts": "git+https://gitlab+deploy-token-5:<EMAIL>/skillspace/microservices/packages/amqp-contracts.git#2.3.3", "@skillspace/lib": "git+https://gitlab+deploy-token-6:<EMAIL>/skillspace/microservices/packages/lib.git#3.5.6", "@skillspace/tracing": "git+https://gitlab+deploy-token-7:<EMAIL>/skillspace/microservices/packages/tracing.git#0.0.1", "bson": "^6.10.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "copyfiles": "^2.4.1", "dotenv": "^16.4.7", "graphql": "^16.10.0", "nodemailer": "^6.10.0", "only-allow": "^1.2.1", "prisma": "^6.4.1", "reflect-metadata": "^0.2.2", "ts-node": "^10.9.2", "twig": "^1.17.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/compat": "^1.2.7", "@golevelup/ts-jest": "^0.6.2", "@nestjs/cli": "^11.0.4", "@nestjs/schematics": "^11.0.1", "@nestjs/testing": "^11.0.10", "@testcontainers/rabbitmq": "^10.18.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.13.5", "@types/nodemailer": "^6.4.15", "@types/supertest": "^6.0.2", "@types/twig": "^1.12.16", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.0.0", "graphql-tag": "^2.12.6", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "prettier": "^3.5.2", "source-map-support": "^0.5.20", "supertest": "^7.0.0", "ts-jest": "^29.2.6", "ts-loader": "^9.5.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}}}