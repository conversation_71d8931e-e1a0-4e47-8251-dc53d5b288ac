image: docker:26.1.4-alpine3.20
variables:
  DOCKER_TLS_CERTDIR: ""
  DOCKER_HOST: tcp://docker:2375
  DOCKER_DRIVER: overlay2
  SERVICE_NAME: emails-service
  SERVICE_IMAGE: gitlab.sksp.site:5050/skillspace/microservices/$SERVICE_NAME/$SERVICE_NAME

stages:
  - build
  - deploy

.base-build:
  stage: build
  services:
    - docker:25.0.1-dind-alpine3.19
  variables:
    DOCKER_BUILDKIT: 1
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - echo "FAILED" > .job_status
    - apk add --update --no-cache curl coreutils openssh-client git
  script:
    # build
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - cat .env.example > .env
    - cd .ci
    - docker compose build
    - docker compose push
    - cd ..
    - echo "SUCCESS" > .job_status
  after_script:
    - cat .job_status
  tags:
    - docker

.base-deploy:
  stage: deploy
  services:
    - docker:25.0.1-dind-alpine3.19
  variables:
    DOCKER_BUILDKIT: 1
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - echo "FAILED" > .job_status
    - apk add --update --no-cache curl coreutils openssh-client helm
  script:
    # deploy
    - cd .ci
    - cat $DOTENV > helm/.env
    - helm upgrade --install $SERVICE_NAME helm/ --namespace $DEPLOY_NAMESPACE --set-string image=$SERVICE_IMAGE:${CI_COMMIT_TAG}
    - cd ..
    - echo "SUCCESS" > .job_status
  after_script:
    - cat .job_status
  tags:
    - docker

build-prod:
  extends: .base-build
  environment:
    name: prod
  rules:
    - if: $CI_COMMIT_TAG

deploy-prod:
  extends: .base-deploy
  environment:
    name: prod
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG =~ /^prod/
