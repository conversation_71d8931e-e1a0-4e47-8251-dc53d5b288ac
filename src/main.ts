import { Tracing } from '@skillspace/tracing';
Tracing.init();

import { NestFactory } from '@nestjs/core';
import { createLogger, LoggerService } from '@skillspace/lib';

import { AppModule } from './app.module';
import { setupGlobalMiddlewares } from './bootstrap-setup';
import { appConfig } from './config/app.config';

async function bootstrap() {
    const app = await NestFactory.create(AppModule, { logger: await createLogger() });
    const logger = app.get(LoggerService);

    setupGlobalMiddlewares(app);

    await app.listen(appConfig.port);
    logger.log(`Email service started on port ${appConfig.port}`);
}

bootstrap();
