import { Module, Provider } from '@nestjs/common';
import { applyOpentelemetryToProvider } from '@skillspace/lib';

import { appConfig, NODE_ENV } from '../config/app.config';
import { SendNewsletterHandler } from './application/commands/send-newsletter';
import { SendTestEmailHandler } from './application/commands/send-test-email';
import { UpdateSmtpSettingsHandler } from './application/commands/update-smtp-settings';
import { GetSchoolSettingsHandler } from './application/queries/get-smtp-settings';
import { SmtpSettingsRepository } from './infrastructure/repositories/smtp-settings.repository';
import { DevSmtpProvider } from './infrastructure/smtp/dev-smtp.provider';
import { SmtpProvider } from './infrastructure/smtp/smtp.provider';
import { TwigTemplateEngine } from './infrastructure/twig-template-engine/twig-template-engine';
import { SMTP_PROVIDER, SMTP_SETTINGS_REPOSITORY, TEMPLATE_ENGINE } from './injects';
import { EmailConsumer } from './presentation/email.consumer';
import { SmtpSettingsResolver } from './presentation/smtp-settings.resolver';

const REPOSITORIES: Provider[] = [
    {
        provide: SMTP_SETTINGS_REPOSITORY,
        useClass: SmtpSettingsRepository,
    },
];

const PROVIDERS: Provider[] = [
    {
        provide: SMTP_PROVIDER,
        useClass:
            appConfig.nodeEnv === NODE_ENV.DEV && appConfig.smtpConsoleOutputEnable ? DevSmtpProvider : SmtpProvider,
    },
    {
        provide: TEMPLATE_ENGINE,
        useClass: TwigTemplateEngine,
    },
];

const COMMAND_HANDLERS = [SendNewsletterHandler, UpdateSmtpSettingsHandler, SendTestEmailHandler];

const QUERY_HANDLERS = [GetSchoolSettingsHandler];

const PRESENTATION = [EmailConsumer, SmtpSettingsResolver];

const EXCEPTION_FILTERS = [];

@Module({
    providers: [
        ...PRESENTATION,
        ...QUERY_HANDLERS,
        ...PROVIDERS,
        ...COMMAND_HANDLERS,
        ...REPOSITORIES,
        ...EXCEPTION_FILTERS,
    ].map((provider) => applyOpentelemetryToProvider(provider)),
})
export class EmailModule {}
