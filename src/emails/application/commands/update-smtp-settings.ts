import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, EventBus, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { CustomError, ERROR_CODE } from '@skillspace/lib';

import { ISmtpProvider } from '../../domain/infrastructure/smtp-provider.interface';
import { ISmtpSettingsRepository } from '../../domain/infrastructure/smtp-settings-repository.interface';
import {
    SmtpSettingsModel,
    SmtpSettingsUpdateParams,
} from '../../domain/models/smtp-settings/smtp-settings-custom.model';
import { User, UserParams } from '../../domain/models/user';
import { SchoolUuid } from '../../domain/objs/school-uuid';
import { SMTP_PROVIDER, SMTP_SETTINGS_REPOSITORY } from '../../injects';

export class UpdateSmtpSettingsCommand implements ICommand {
    constructor(
        public readonly user: UserParams,
        public readonly schoolUuid: string,
        public readonly updateParams: SmtpSettingsUpdateParams,
    ) {}
}

@CommandHandler(UpdateSmtpSettingsCommand)
export class UpdateSmtpSettingsHandler implements ICommandHandler<UpdateSmtpSettingsCommand> {
    constructor(
        @Inject(SMTP_SETTINGS_REPOSITORY)
        private readonly smtpRepo: ISmtpSettingsRepository,
        @Inject(SMTP_PROVIDER)
        private readonly smtpProvider: ISmtpProvider,
        private readonly eventBus: EventBus,
    ) {}

    private readonly logger = new Logger(UpdateSmtpSettingsHandler.name);

    async execute(command: UpdateSmtpSettingsCommand): Promise<SmtpSettingsModel> {
        const anUser = User.create(command.user);
        const schoolUuid = SchoolUuid.create(command.schoolUuid).unwrap();
        const aSmtpSettings = await this.smtpRepo.findOne(schoolUuid);

        if (!aSmtpSettings) {
            const anCreatedSettings = SmtpSettingsModel.create(anUser, schoolUuid, command.updateParams);
            await this.smtpRepo.insert(anCreatedSettings);
            this.logger.log(anCreatedSettings.logParams, `Созданы настройки для школы ID: ${schoolUuid}`);
            return anCreatedSettings;
        }

        const anUpdatedSmtpSettings = aSmtpSettings.update(anUser, schoolUuid, command.updateParams);

        if (!anUpdatedSmtpSettings.isTestRequired) {
            await this.smtpRepo.update(anUpdatedSmtpSettings);
            this.logger.log(anUpdatedSmtpSettings.logParams, `Обновлены настройки для школы ID: ${schoolUuid}`);
            return anUpdatedSmtpSettings;
        }

        // если включаются кастомные настройки, проверяем SMTP
        try {
            let timeoutId: NodeJS.Timeout;

            await Promise.race([
                this.smtpProvider.verifySmtpSettings(aSmtpSettings.transportOptions),
                new Promise((_, reject) => {
                    timeoutId = setTimeout(() => {
                        reject(
                            new CustomError('Проверка SMTP заняла более 10 секунд', {
                                code: ERROR_CODE.TIMEOUT_ERROR,
                            }),
                        );
                    }, 10_000);
                }),
            ]);

            clearTimeout(timeoutId);
        } catch (e) {
            throw new CustomError('Ошибка при тесте SMTP настроек', {
                code: ERROR_CODE.INTEGRATION_ERROR,
                cause: e,
                details: { ...aSmtpSettings.logParams, message: e.message },
            });
        }
        return await this.switchSettingsOn(anUpdatedSmtpSettings);
    }

    private async switchSettingsOn(aSettings: SmtpSettingsModel): Promise<SmtpSettingsModel> {
        await this.smtpRepo.update(aSettings);
        this.logger.log(aSettings.logParams, `Включены кастомные настройки для школы ID: ${aSettings.schoolUuid}`);
        return aSettings;
    }
}
