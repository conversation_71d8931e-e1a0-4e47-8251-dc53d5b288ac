import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { ICommand } from '@nestjs/cqrs';
import { CustomError, ERROR_CODE } from '@skillspace/lib';

import { ISmtpProvider } from '../../domain/infrastructure/smtp-provider.interface';
import { ISmtpSettingsRepository } from '../../domain/infrastructure/smtp-settings-repository.interface';
import { ITemplateEngine } from '../../domain/infrastructure/template-engine.interface';
import { NewsletterModel } from '../../domain/models/newsletter/newsletter.model';
import { SchoolUuid } from '../../domain/objs/school-uuid';
import { SMTP_PROVIDER, SMTP_SETTINGS_REPOSITORY, TEMPLATE_ENGINE } from '../../injects';

export class SendTestEmailCommand implements ICommand {
    constructor(public readonly params: { email: string; schoolUuid: string }) {}
}

@CommandHandler(SendTestEmailCommand)
export class SendTestE<PERSON>Handler implements ICommandHandler<SendTestEmailCommand> {
    constructor(
        @Inject(SMTP_SETTINGS_REPOSITORY)
        private readonly smtpRepo: ISmtpSettingsRepository,
        @Inject(SMTP_PROVIDER)
        private readonly smtpProvider: ISmtpProvider,
        @Inject(TEMPLATE_ENGINE)
        private readonly templateEngine: ITemplateEngine,
    ) {}

    private readonly logger = new Logger(SendTestEmailHandler.name);

    async execute(command: SendTestEmailCommand): Promise<any> {
        const schoolUuid = SchoolUuid.create(command.params.schoolUuid).unwrap();
        const aSmtpSettings = await this.smtpRepo.findOne(schoolUuid);
        if (!aSmtpSettings) {
            throw new CustomError('Тест отправки письма: не найдены настройки', {
                details: { schoolUuid },
            });
        }

        const aNewsLetter = NewsletterModel.create(
            {
                recipients: [command.params.email],
                subject: 'Тест настроек SMTP',
                body: {},
                template: 'test_smtp',
            },
            aSmtpSettings,
            {},
        ).generate(this.templateEngine);

        this.logger.verbose({ ...aNewsLetter.logParams }, `Отправляем тестовое письмо`);

        try {
            await this.smtpProvider.sendEmail(aSmtpSettings.transportOptions, aNewsLetter.newsletter);
            this.logger.log({ ...aNewsLetter.logParams }, `Тестовое письмо успешно отправлено`);
        } catch (error) {
            throw new CustomError(`Ошибка отправки тестового письма от ${aNewsLetter.logParams.from}`, {
                code: ERROR_CODE.INTEGRATION_ERROR,
                cause: error,
                details: {
                    ...aNewsLetter.logParams,
                    message: error.message,
                },
            });
        }
    }
}
