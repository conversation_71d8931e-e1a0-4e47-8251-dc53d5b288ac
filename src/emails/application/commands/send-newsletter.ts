import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { ICommand } from '@nestjs/cqrs';
import { EmailServiceSendEmailToManyContractNamespace } from '@skillspace/amqp-contracts';
import { CustomError, ERROR_CODE } from '@skillspace/lib';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

import { getValidationErrorMessages } from '../../../shared/utils/get-validation-error-messages';
import { ISmtpProvider } from '../../domain/infrastructure/smtp-provider.interface';
import { ISmtpSettingsRepository } from '../../domain/infrastructure/smtp-settings-repository.interface';
import { ITemplateEngine } from '../../domain/infrastructure/template-engine.interface';
import { NewsletterModel, NewsletterParams } from '../../domain/models/newsletter/newsletter.model';
import { SmtpSettingsAbstract } from '../../domain/models/smtp-settings/smtp-settings.abstract';
import { SmtpSettingsDefaultModel } from '../../domain/models/smtp-settings/smtp-settings-default.model';
import { SMTP_PROVIDER, SMTP_SETTINGS_REPOSITORY, TEMPLATE_ENGINE } from '../../injects';

export class SendNewsletterCommand implements ICommand {
    constructor(public readonly payload: NewsletterParams & { schoolUuid?: string }) {}
}

@CommandHandler(SendNewsletterCommand)
export class SendNewsletterHandler implements ICommandHandler<SendNewsletterCommand> {
    constructor(
        @Inject(SMTP_SETTINGS_REPOSITORY)
        private readonly smtpRepo: ISmtpSettingsRepository,
        @Inject(SMTP_PROVIDER)
        private readonly smtpProvider: ISmtpProvider,
        @Inject(TEMPLATE_ENGINE)
        private readonly templateEngine: ITemplateEngine,
    ) {}

    private readonly logger = new Logger(SendNewsletterHandler.name);

    async execute(command: SendNewsletterCommand): Promise<any> {
        const payload = plainToClass(EmailServiceSendEmailToManyContractNamespace.RequestPayload, command.payload);
        const errors = await validate(payload);

        if (errors.length) {
            const errorMessages = getValidationErrorMessages(errors);
            throw new CustomError(`Ошибка валидации сообщения "${payload.subject}"`, {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: {
                    errors: errorMessages,
                    ...payload,
                },
            });
        }

        const aSmtpSettings = await this.getSmtpSettings(command.payload.schoolUuid);
        const aNewsLetter = NewsletterModel.create(command.payload, aSmtpSettings, {
            name: payload?.fromName,
        }).generate(this.templateEngine);

        this.logger.log({ ...aNewsLetter.logParams }, `Отправляем письмо "${aNewsLetter.logParams.subject}"`);

        try {
            await this.smtpProvider.sendEmail(aSmtpSettings.transportOptions, aNewsLetter.newsletter);
            this.logger.log(
                { ...aNewsLetter.logParams },
                `Письмо "${aNewsLetter.logParams.subject}" успешно отправлено`,
            );
        } catch (error) {
            const { smtp, to } = aNewsLetter.logParams;
            throw new CustomError(`Ошибка отправки письма для ${to} от ${smtp.email}: ${error.message.slice(0, 60)}`, {
                code: ERROR_CODE.INTEGRATION_ERROR,
                cause: error,
                details: {
                    ...aNewsLetter.logParams,
                    message: error.message,
                },
            });
        }
    }

    private async getSmtpSettings(schoolUuid?: string): Promise<SmtpSettingsAbstract> {
        const aDefaultSettings = SmtpSettingsDefaultModel.create();
        if (!schoolUuid) {
            return aDefaultSettings;
        }

        const aSchoolSettings = await this.smtpRepo.findOne(schoolUuid);

        return !aSchoolSettings || !aSchoolSettings.useCustomSmtp ? aDefaultSettings : aSchoolSettings;
    }
}
