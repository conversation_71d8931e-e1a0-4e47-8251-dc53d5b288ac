import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryH<PERSON><PERSON> } from '@nestjs/cqrs';
import { CustomError, ERROR_CODE } from '@skillspace/lib';

import { ISmtpSettingsRepository } from '../../domain/infrastructure/smtp-settings-repository.interface';
import { User, UserParams } from '../../domain/models/user';
import { SchoolUuid } from '../../domain/objs/school-uuid';
import { SMTP_SETTINGS_REPOSITORY } from '../../injects';
import { SmtpSettingsQueryResult } from './results/get-smtp-settings.result';

export class GetSmtpSettingsQuery implements IQuery {
    constructor(
        public readonly user: UserParams,
        public readonly schoolUuid: string,
    ) {}
}

@QueryHandler(GetSmtpSettingsQuery)
export class GetSchoolSettingsHandler implements IQueryHandler<GetSmtpSettingsQuery> {
    constructor(
        @Inject(SMTP_SETTINGS_REPOSITORY)
        private readonly smtpRepo: ISmtpSettingsRepository,
    ) {}

    async execute(query: GetSmtpSettingsQuery): Promise<SmtpSettingsQueryResult> {
        const anUser = User.create(query.user);
        const schoolUuid = SchoolUuid.create(query.schoolUuid).unwrap();
        const aSchoolSettings = await this.smtpRepo.findOne(schoolUuid);

        if (!aSchoolSettings) {
            throw new CustomError(`Настройки школы ${schoolUuid} не найдены`, {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { schoolUuid },
            });
        }

        if (!anUser.hasPermissionToRead(schoolUuid)) {
            throw new CustomError('Нет хватает прав для просмотра smtp настроек школы', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { schoolUuid, user: anUser.logParams },
            });
        }

        return new SmtpSettingsQueryResult(aSchoolSettings);
    }
}
