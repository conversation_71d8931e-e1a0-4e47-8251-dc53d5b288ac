import { IQueryResult } from '@nestjs/cqrs';

import { SmtpSettingsModel } from '../../../domain/models/smtp-settings/smtp-settings-custom.model';

export interface ISmtpSettingsResult {
    useCustomSmtp: boolean;
    host: string;
    port: number;
    user: string;
    password: string;
    email: string;
}

export class SmtpSettingsQueryResult implements IQueryResult, ISmtpSettingsResult {
    public schoolUuid: string;
    public useCustomSmtp: boolean;
    public host: string;
    public port: number;
    public user: string;
    public password: string;
    public email: string;

    constructor(private readonly model: SmtpSettingsModel) {
        Object.assign(this, model);
    }
}
