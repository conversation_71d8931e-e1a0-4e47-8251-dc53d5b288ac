import { Field, InputType, Int } from '@nestjs/graphql';

import { SmtpSettingsUpdateParams } from '../../domain/models/smtp-settings/smtp-settings-custom.model';

@InputType()
export class UpdateSmtpSettingsInput implements SmtpSettingsUpdateParams {
    @Field({ nullable: true })
    useCustomSmtp?: boolean = false;

    @Field({ nullable: true })
    host?: string;

    @Field(() => Int, { nullable: true })
    port?: number;

    @Field({ nullable: true })
    user?: string;

    @Field({ nullable: true })
    password?: string;

    @Field({ nullable: true })
    email?: string;
}
