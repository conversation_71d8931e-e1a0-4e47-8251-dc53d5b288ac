import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { GqlSchoolId, GqlUser } from '@skillspace/lib';

import { EmailsUserContext } from '../../shared/lib/user-context.type';
import { SendTestEmailCommand } from '../application/commands/send-test-email';
import { UpdateSmtpSettingsCommand } from '../application/commands/update-smtp-settings';
import { GetSmtpSettingsQuery } from '../application/queries/get-smtp-settings';
import { SmtpSettingsQueryResult } from '../application/queries/results/get-smtp-settings.result';
import { UpdateSmtpSettingsInput } from './inputs/update-smtp-settings.input';
import { SmtpSettingsOutput } from './outputs/school-settings.output';
import { VoidResponse } from './outputs/void-response.output';

@Resolver(() => SmtpSettingsOutput)
export class SmtpSettingsResolver {
    constructor(
        private readonly commandBus: CommandBus,
        private readonly queryBus: QueryBus,
    ) {}

    @Query(() => SmtpSettingsOutput, { nullable: true, name: 'smtpSettings' })
    async getSmtpSettings(
        @GqlUser() user: EmailsUserContext,
        @GqlSchoolId() schoolUuid: string,
    ): Promise<SmtpSettingsOutput | null> {
        const userContext = { ...user, schoolUuid };
        return this.queryBus.execute(new GetSmtpSettingsQuery(userContext, schoolUuid));
    }

    @Mutation(() => VoidResponse, { nullable: true, name: 'testSmtpSettings' })
    async testSmtpSettings(
        @GqlUser() user: EmailsUserContext,
        @GqlSchoolId() schoolUuid: string,
    ): Promise<VoidResponse> {
        try {
            await this.commandBus.execute(new SendTestEmailCommand({ email: user.email, schoolUuid }));
            return { success: true };
        } catch (e) {
            return { success: false, errorMessage: e.message };
        }
    }

    @Mutation(() => SmtpSettingsOutput, { nullable: true, name: 'updateSmtpSettings' })
    async updateSmtpSettings(
        @GqlUser() user: EmailsUserContext,
        @GqlSchoolId() schoolUuid: string,
        @Args('params', { type: () => UpdateSmtpSettingsInput })
        params: UpdateSmtpSettingsInput,
    ): Promise<SmtpSettingsOutput> {
        const userContext = { ...user, schoolUuid };
        const anSchoolSettings = await this.commandBus.execute(
            new UpdateSmtpSettingsCommand(userContext, schoolUuid, params),
        );
        return new SmtpSettingsQueryResult(anSchoolSettings);
    }
}
