import { Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import {
    EmailServiceSendEmailToManyContract,
    EmailServiceSendEmailToManyContractNamespace,
} from '@skillspace/amqp-contracts';
import { AmqpSubscribe, RabbitPayload } from '@skillspace/lib';

import { SendNewsletterCommand } from '../application/commands/send-newsletter';

@Injectable()
export class EmailConsumer {
    constructor(private readonly commandBus: CommandBus) {}
    @AmqpSubscribe(EmailServiceSendEmailToManyContract)
    async sendEmailToMany(
        @RabbitPayload() message: EmailServiceSendEmailToManyContractNamespace.Message,
    ): Promise<void> {
        await this.commandBus.execute(new SendNewsletterCommand(message.payload));
    }
}
