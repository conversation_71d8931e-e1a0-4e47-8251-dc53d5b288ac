import { Field, ObjectType } from '@nestjs/graphql';

import { ISmtpSettingsResult } from '../../application/queries/results/get-smtp-settings.result';

@ObjectType()
export class SmtpSettingsOutput implements ISmtpSettingsResult {
    @Field()
    useCustomSmtp: boolean;

    @Field()
    host: string;

    @Field()
    port: number;

    @Field()
    user: string;

    @Field()
    password: string;

    @Field()
    email: string;
}
