{% if (textOnly is defined and textOnly == false) or textOnly is not defined %}
    {% if primaryColor is not defined %}
        {% set primaryColor = "#17B198" %}
    {% endif %}
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=400" />
        <title></title>
        <link rel="shortcut icon" href="https://tilda.ws/img/tildafavicon.ico" />
        <style type="text/css">	.ExternalClass {width:100%;}	img{ border:0 none; height:auto; line-height:100%; outline:none; text-decoration:none; -ms-interpolation-mode: bicubic;	}	a img{ border:0 none;	}	#outlook a {padding:0;}	#allrecords{ height:100% !important; margin:0; padding:0; width:100% !important; -webkit-font-smoothing: antialiased; line-height: 1.45;	}	#allrecords td{ margin:0; padding:0;	}	#allrecords ul{-webkit-padding-start:30px;}	@media only screen and (max-width: 720px) { .r{	width:100% !important;	min-width:400px !important; }	}	@media only screen and (max-width: 480px) { .t-emailBlock { display: block !important; padding-left: 0 !important; padding-right: 0 !important; width: 100% !important; } .t-emailBlockPadding { padding-top: 15px !important; } .t-emailBlockPadding30 { padding-top: 30px !important; } .t-emailAlignLeft { text-align: left !important; margin-left: 0 !important; } .t-emailAlignCenter { text-align: center !important; margin-left: auto !important; margin-right: auto !important; }	}</style>
    </head>
    <body cellpadding="0" cellspacing="0" style="padding: 0; margin: 0; border: 0; width:100%; -webkit-text-size-adjust:100%; -ms-text-size-adjust:100%; background-color: #ffffff;">
        <!--allrecords-->
        <table id="allrecords" data-tilda-email="yes" data-tilda-project-id="3722616" data-tilda-page-id="17523552" data-tilda-page-alias="" cellpadding="0" cellspacing="0" style="width:100%; border-collapse:collapse; border-spacing:0; padding:0; margin:0; border:0;">
            <tr>
                <td style="background-color: #ffffff; " >
                    <!--record_mail-->
                    <table id="rec283975824" style="width:100%; border-collapse:collapse; border-spacing:0; margin:0; border:0;" cellpadding="0" cellspacing="0" data-record-type="324">
                        <tr>
                            <td style="padding-left:15px; padding-right:15px; background-color:#f0f0f0;">
                                <table id="recin283975824" class="r" style="margin: 0 auto;border-spacing: 0;width:720px;" align="center">
                                    <tr>
                                        <td style="padding: 30px 0 0;">
                                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
{#                                                <tr>#}
{#                                                    <td style="text-align: center; padding: 60px 30px 0;background-color:#ffffff; border-radius: 8px 8px 0 0;">#}
{#                                                        {% if block("buttonLink") is defined %}#}
{#                                                            <a href="{{ block("buttonLink") }}" style="text-decoration: none; color: #4d5766;">#}
{#                                                        {% else %}#}
{#                                                           <a style="text-decoration: none; color: #4d5766;">#}
{#                                                        {% endif %}#}
{#                                                            <div style="margin: 0 auto; font-weight: normal; font-family: Helvetica Neue, Helvetica, Arial, sans-serif; color:#4d5766;font-size:26px;line-height:1.3;max-width:400px;">#}
{#                                                                #}{# Логотип #}
{#                                                                {% if isWhiteLabelHideLogo %}#}
{#                                                                    {% if schoolLogoUrl is defined and schoolLogoUrl is not null %}#}
{#                                                                        <img src="{{ schoolLogoUrl }}" alt="" style="max-height: 80px; max-width: 300px;">#}
{#                                                                    {% endif %}#}
{#                                                                {% else %}#}
{#                                                                    <img src="https://skillspace.ru/img/common/logo-gray.svg" style="width: 200px;" alt="">#}
{#                                                                {% endif %}#}
{#                                                            </div>#}
{#                                                        </a>#}
{#                                                    </td>#}
{#                                                </tr>#}
                                                <tr>
                                                    <td style="text-align: center; padding: 90px 30px 15px;background-color:#ffffff;border-radius: 8px 8px 0 0;">
                                                        {% if block("buttonLink") is defined %}
                                                            <a href="{{ block("buttonLink") }}" style="text-decoration: none; color: #4d5766;">
                                                        {% else %}
                                                            <a style="text-decoration: none; color: #4d5766;">
                                                        {% endif %}
                                                            <div style="margin: 0 auto; font-weight: normal; font-family: Helvetica Neue, Helvetica, Arial, sans-serif; color:#4d5766;font-size:26px;line-height:1.3;max-width:400px;">
                                                                {# Заголовок #}
                                                                {% if block("title") is defined %}
                                                                    {{ block('title') }}
                                                                {% endif %}
                                                            </div>
                                                        </a>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; padding: 30px 30px 10px;background-color:#ffffff;">
                                                        <div style="margin: 0 auto; font-weight: normal; font-family: Helvetica Neue, Helvetica, Arial, sans-serif; color:#4d5766;font-size:16px;line-height:1.55;max-width:450px;">
                                                            {# Тело сообщения #}
                                                            {% if block("body") is defined %}
                                                                {{ block('body') }}
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% if block("buttonText") is defined and block("buttonLink") is defined %}
                                                    {# Кнопка #}
                                                    <tr>
                                                        <td
                                                                style="padding: 24px 30px 0;background-color:#ffffff;"
                                                        >
                                                            <table
                                                                    border="0"
                                                                    cellpadding="0"
                                                                    cellspacing="0"
                                                                    align="center"
                                                                    style="margin: 0 auto;"
                                                            >
                                                                <tr>
                                                                    <td>
                                                                        <!--[if mso]>
                                                                        <v:roundrect
                                                                                xmlns:v="urn:schemas-microsoft-com:vml"
                                                                                xmlns:w="urn:schemas-microsoft-com:office:word"
                                                                                href="{{ block('buttonLink') }}"
                                                                                style="height:52px;v-text-anchor:middle;mso-wrap-style:none;mso-position-horizontal:center;"
                                                                                arcsize="24%"
                                                                                stroke="f"
                                                                                fillcolor="{{ primaryColor }}"
                                                                        >
                                                                        <w:anchorlock />
                                                                        <center
                                                                                style="text-decoration: none; padding: 15px 30px; font-size: 15px; text-align: center; font-weight: bold; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; width: 100%;color:#ffffff;"
                                                                        >
                                                                            {{ block('buttonText') }}
                                                                        </center>
                                                                        </v:roundrect>
                                                                        <![endif]-->
                                                                        <!--[if !mso]-->
                                                                        <a
                                                                                style="display: table-cell; text-decoration: none; padding: 15px 30px; font-size: 15px; text-align: center; font-weight: bold; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; width: 100%;color:#ffffff; background-color:{{ primaryColor }}; border-radius: 8px;"
                                                                                href="{{ block('buttonLink') }}"
                                                                        >
                                                                            {{ block('buttonText') }}
                                                                        </a>
                                                                        <!--[endif]-->
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                {% endif %}
                                                {%
                                                    if block("secondButtonText") is defined
                                                    and block("secondButtonLink") is defined
                                                    and block("secondButtonText") != ''
                                                    and block("secondButtonLink") != ''
                                                %}
                                                    {# Кнопка второстепенная #}
                                                    <tr>
                                                        <td
                                                                style="padding: 14px 30px 0;background-color:#ffffff;"
                                                        >
                                                            <table
                                                                    border="0"
                                                                    cellpadding="0"
                                                                    cellspacing="0"
                                                                    align="center"
                                                                    style="margin: 0 auto;"
                                                            >
                                                                <tr>
                                                                    <td>
                                                                        <!--[if mso]>
                                                                        <v:roundrect
                                                                                xmlns:v="urn:schemas-microsoft-com:vml"
                                                                                xmlns:w="urn:schemas-microsoft-com:office:word"
                                                                                href="{{ block('secondButtonLink') }}"
                                                                                style="height:52px;v-text-anchor:middle;mso-wrap-style:none;mso-position-horizontal:center;"
                                                                                arcsize="24%"
                                                                                stroke="f"
                                                                                fillcolor="{{ primaryColor }}1c"
                                                                        >
                                                                        <w:anchorlock />
                                                                        <center
                                                                                style="text-decoration: none; padding: 15px 30px; font-size: 15px; text-align: center; font-weight: bold; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; width: 100%;color:{{ primaryColor }};"
                                                                        >
                                                                            {{ block('secondButtonText') }}
                                                                        </center>
                                                                        </v:roundrect>
                                                                        <![endif]-->
                                                                        <!--[if !mso]-->
                                                                        <a
                                                                            style="display: table-cell; text-decoration: none; padding: 15px 30px; font-size: 15px; text-align: center; font-weight: bold; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; width: 100%;color:{{ primaryColor }}; background-color:{{ primaryColor }}1c; border-radius: 8px;"
                                                                            href="{{ block('secondButtonLink') }}"
                                                                            target="_blank"
                                                                        >
                                                                            {{ block('secondButtonText') }}
                                                                        </a>
                                                                        <!--[endif]-->
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                {% endif %}
                                                {% if
                                                    hideMobileAppLinks is not defined or hideMobileAppLinks == false
                                                %}
                                                <tr>
                                                    <td style="height: 20px;background-color:#ffffff;"> </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; padding: 30px 30px 10px;background-color:#ffffff;">
                                                        <div style="margin: 0 auto; font-weight: normal; font-family: Helvetica Neue, Helvetica, Arial, sans-serif; color:#4d5766;font-size:16px;line-height:1.55;max-width:450px;">
                                                            Обучаться еще удобнее в мобильном приложении:
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td
                                                        style="padding: 14px 30px 0;background-color:#ffffff;"
                                                    >
                                                        <table
                                                            border="0"
                                                            cellpadding="0"
                                                            cellspacing="0"
                                                            align="center"
                                                            style="margin: 0 auto;"
                                                        >
                                                            <tr>
                                                                <td>
                                                                    <!--[if mso]>
                                                                        <v:roundrect
                                                                                xmlns:v="urn:schemas-microsoft-com:vml"
                                                                                xmlns:w="urn:schemas-microsoft-com:office:word"
                                                                                href="https://play.google.com/store/apps/details?id=ru.skillspace.client"
                                                                                style="height:52px;v-text-anchor:middle;mso-wrap-style:none;mso-position-horizontal:center;"
                                                                                arcsize="24%"
                                                                                stroke="f"
                                                                                fillcolor="#4d5766"
                                                                        >
                                                                        <w:anchorlock />
                                                                        <center
                                                                                style="text-decoration: none; padding: 15px 30px; font-size: 15px; text-align: center; font-weight: bold; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; width: 100%;color:{{ primaryColor }};"
                                                                        >
                                                                            Доступно в Google Play
                                                                        </center>
                                                                        </v:roundrect>
                                                                        <![endif]-->
                                                                    <!--[if !mso]-->
                                                                    <a
                                                                        style="display: table-cell; text-decoration: none; padding: 15px 30px; font-size: 15px; text-align: center; font-weight: bold; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; width: 100%;color: #fff; background-color:#4d5766; border-radius: 8px;"
                                                                        href="https://play.google.com/store/apps/details?id=ru.skillspace.client"
                                                                        target="_blank"
                                                                    >
                                                                        <img src="https://ebyddyi.stripocdn.email/content/guids/CABINET_cea639e5da290d3f853cda5474e3e4e338c7de5f3db215445c9f4b8c10541a3d/images/googleplay_7kX.png" alt="icon" width="20" style="margin-right:10px" align="absmiddle">
                                                                        Доступно в Google Play
                                                                    </a>
                                                                    <!--[endif]-->
                                                                </td>

                                                                <td style="padding-left: 20px;">
                                                                    <!--[if mso]>
                                                                        <v:roundrect
                                                                                xmlns:v="urn:schemas-microsoft-com:vml"
                                                                                xmlns:w="urn:schemas-microsoft-com:office:word"
                                                                                href="https://apps.apple.com/ru/app/skillspace/id1578915606"
                                                                                style="height:52px;v-text-anchor:middle;mso-wrap-style:none;mso-position-horizontal:center;"
                                                                                arcsize="24%"
                                                                                stroke="f"
                                                                                fillcolor="#4d5766"
                                                                        >
                                                                        <w:anchorlock />
                                                                        <center
                                                                                style="text-decoration: none; padding: 15px 30px; font-size: 15px; text-align: center; font-weight: bold; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; width: 100%;color: #4d5766;border-color:#A6ABB2;border-style:solid;border-width:1px;"
                                                                        >
                                                                            Доступно в App Store
                                                                        </center>
                                                                        </v:roundrect>
                                                                        <![endif]-->
                                                                    <!--[if !mso]-->
                                                                    <a
                                                                        style="display: inline-block; text-decoration: none; padding: 13px 30px; font-size: 15px; text-align: center; font-weight: bold; font-family:Helvetica Neue, Helvetica, Arial, sans-serif; width: 100%;color: #4d5766; background-color:#fff; border-radius: 8px;border-color:#A6ABB2;border-style:solid;border-width:1px;"
                                                                        href="https://apps.apple.com/ru/app/skillspace/id1578915606"
                                                                        target="_blank"
                                                                    >
                                                                        <img src="https://ebyddyi.stripocdn.email/content/guids/CABINET_cea639e5da290d3f853cda5474e3e4e338c7de5f3db215445c9f4b8c10541a3d/images/appstore.png" alt="icon" width="20" style="margin-right:10px; margin-bottom: 2px;" align="absmiddle">
                                                                        Доступно в App Store
                                                                    </a>
                                                                    <!--[endif]-->
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                {% endif %}
                                                <tr>
                                                    <td style="height: 50px;background-color:#ffffff;border-radius: 0 0 8px 8px;"> </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <!--/record--><!--record_mail-->
                    <table id="rec283980406" style="width:100%; border-collapse:collapse; border-spacing:0; margin:0; border:0;" cellpadding="0" cellspacing="0" data-record-type="627">
                        <tr>
                            <td style="padding-left:15px; padding-right:15px; background-color:#f0f0f0;">
                                <table id="recin283980406" class="r" style="margin: 0 auto;border-spacing: 0;width:720px;" align="center">
                                    <tr>
                                        <td style="padding-top:30px;padding-bottom:45px;padding-left:30px;padding-right:30px;">
                                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td style="text-align: center; padding-top: 0;">
                                                        <div style="margin: 0 auto; font-weight: normal; font-family: Helvetica Neue, Helvetica, Arial, sans-serif; color:#7E858F;font-size:14px;">
                                                            <div style="color:#7E858F;" data-customstyle="yes">
                                                                {% if
                                                                    isWhiteLabel is defined
                                                                    and isWhiteLabel
                                                                %}
                                                                {% else %}
                                                                    <a href="https://t.me/skillspaceru" style="color: rgb(126, 133, 143);text-decoration: none;">Наш Telegram</a> /
                                                                    <a href="mailto:<EMAIL>" style="color: rgb(126, 133, 143);text-decoration: none;"><EMAIL></a> /
                                                                {% endif %}
                                                                {% if unSubscriberUrl is defined and unSubscriberUrl|length > 0 %}
                                                                    <a href="{{ unSubscriberUrl }}" target="_blank" style="color: rgb(126, 133, 143);text-decoration: none;">
                                                                        Отписаться
                                                                    </a>
                                                                {% endif %}

                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <!--/record-->
                </td>
            </tr>
        </table>
        <!--/allrecords-->
    </body>
    </html>
{% else %}
    {% if block("overtitle") is defined %}
        {{ block('overtitle')|striptags }}
    {% endif %}

    {% if block("title") is defined %}
        {{ block('title')|striptags }}
    {% endif %}

    {% if block("body") is defined %}
        {{ block('body')|striptags }}
    {% endif %}

    {% if block("buttonText") is defined %}
        {{ block('buttonText')|striptags }}
    {% endif %}

    {% if block("buttonLink") is defined %}
        {{ block('buttonLink')|striptags }}
    {% endif %}

    Если появятся вопросы или потребуется помощь -- напишите нам в онлайн-чат на сайте: обычно мы отвечаем в течение 5 минут!

    Наш Telegram: https://t.me/skillspaceru
    Поддержка: <EMAIL>

    {% if unSubscriberUrl is defined and unSubscriberUrl|length > 0 %}
        <a href="{{ unSubscriberUrl }}" target="_blank">
            Отписаться
        </a>
    {% endif %}
{% endif %}
