{% extends 'template.html.twig' %}

{% block title %}
    Осталось установить пароль и войти в аккаунт
{% endblock %}

{% block body %}
    {% if course is defined and course is not null %}
        Вам открыт доступ к курсу «{{ course.name }}».
    {% elseif categories is defined and categories|length > 0 %}
        Вам открыт доступ к курсам из категорий
        {% for category in categories %}
            &laquo;{{ category.name }}&raquo;{% if loop.last == false %},{% else %}.{% endif %}
        {% endfor %}
    {% elseif courses is defined and courses|length > 0 %}
        Вам открыт доступ к курсам
        {% for course in courses %}
            &laquo;{{ course.name }}&raquo;{% if loop.last == false %},{% else %}.{% endif %}
        {% endfor %}
    {% endif %}

    <br>

    Чтобы войти в аккаунт и начать обучение, нажмите на кнопку ниже и установите пароль:
{% endblock %}

{% block buttonText %}
    Войти в аккаунт
{% endblock %}

{% block buttonLink %}
    {% if course is defined and course is not null %}
        {% if schoolDomain is empty %}
            https://{{ schoolSlug }}.skillspace.ru/course/{{ course.id }}?registerToken={{ changePasswordToken }}
        {% else %}
            https://{{ schoolDomain }}/course/{{ course.id }}?registerToken={{ changePasswordToken }}
        {% endif %}
    {% else %}
        {% if schoolDomain is empty %}
            https://{{ schoolSlug }}.skillspace.ru/?registerToken={{ changePasswordToken }}
        {% else %}
            https://{{ schoolDomain }}/?registerToken={{ changePasswordToken }}
        {% endif %}
    {% endif %}
{% endblock %}

{% block secondButtonText %}
    {%- if isWhiteLabel == false %}
        Проблемы со входом?
    {% endif -%}
{% endblock %}

{% block secondButtonLink %}
    {%- if isWhiteLabel == false %}
        https://help.skillspace.ru/tpost/ntc81sly91-problema-ne-poluchaetsya-voiti-v-akkaunt
    {% endif -%}
{% endblock %}