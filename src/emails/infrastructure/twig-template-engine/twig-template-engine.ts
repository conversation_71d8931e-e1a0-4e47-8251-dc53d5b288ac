import * as assert from 'node:assert';
import * as fs from 'node:fs';
import * as path from 'node:path';

import { Injectable } from '@nestjs/common';
import { CustomError, ERROR_CODE } from '@skillspace/lib';
import Twig, { twig } from 'twig';

import { ITemplateEngine } from '../../domain/infrastructure/template-engine.interface';
import { EmailTemplate } from '../../domain/models/email-template.factory';

@Injectable()
export class TwigTemplateEngine implements ITemplateEngine {
    private availableTemplates: Set<string>;
    private templatesDir = path.join(__dirname, 'twig-templates');

    constructor() {
        this.availableTemplates = new Set();
        this.loadAvailableTemplates();
    }

    public generateHtml(aTemplate: EmailTemplate): string {
        try {
            return this.getTemplate(aTemplate).render(aTemplate);
        } catch (error) {
            throw new CustomError('Ошибка генерации html', {
                code: ERROR_CODE.INTERNAL_ERROR,
                cause: error,
                details: {
                    template: aTemplate.template,
                    params: aTemplate.params,
                },
            });
        }
    }

    public generateText(aTemplate: EmailTemplate): string {
        try {
            return this.getTemplate(aTemplate).render({ ...aTemplate, textOnly: true });
        } catch (error) {
            throw new CustomError('Ошибка генерации текстовой версии письма', {
                code: ERROR_CODE.INTERNAL_ERROR,
                cause: error,
                details: {
                    template: aTemplate.template,
                    params: aTemplate.params,
                },
            });
        }
    }

    private getTemplate(aTemplate: EmailTemplate): Twig.Template {
        const templateName = aTemplate.template;

        assert(this.availableTemplates.has(templateName), `Шаблон не найден: ${templateName}`);

        const templatePath = path.join(this.templatesDir, `${templateName}.html.twig`);

        return twig({
            path: templatePath,
            async: false,
        });
    }

    private loadAvailableTemplates() {
        fs.readdir(this.templatesDir, (err, files) => {
            files
                .filter((file) => file.endsWith('.html.twig'))
                .forEach((file) => {
                    const templateName = path.basename(file, '.html.twig');
                    this.availableTemplates.add(templateName);
                });
        });
    }
}
