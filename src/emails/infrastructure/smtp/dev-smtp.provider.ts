import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

import { ISmtpProvider, Newsletter } from '../../domain/infrastructure/smtp-provider.interface';
import { SmtpTargetOptions } from '../../domain/models/smtp-settings/smtp-settings.abstract';

@Injectable()
export class DevSmtpProvider implements ISmtpProvider {
    async sendEmail(options: SmtpTargetOptions, newsLetter: Newsletter): Promise<void> {
        const account = await nodemailer.createTestAccount();

        const client = nodemailer.createTransport({
            host: account.smtp.host,
            port: account.smtp.port,
            secure: false,
            auth: {
                user: account.user,
                pass: account.pass,
            },
            authMethod: 'PLAIN',
        });

        const info = await client.sendMail(newsLetter);

        console.log('Message sent: %s', info.messageId);

        if (nodemailer.getTestMessageUrl) {
            const previewUrl = nodemailer.getTestMessageUrl(info);
            if (previewUrl) {
                console.log('Preview URL: %s', previewUrl);
            }
        }
    }
    async verifySmtpSettings(): Promise<void> {
        const account = await nodemailer.createTestAccount();

        const client = nodemailer.createTransport({
            host: account.smtp.host,
            port: account.smtp.port,
            secure: false,
            auth: {
                user: account.user,
                pass: account.pass,
            },
            authMethod: 'PLAIN',
        });
        await client.verify();
        client.close();
    }
}
