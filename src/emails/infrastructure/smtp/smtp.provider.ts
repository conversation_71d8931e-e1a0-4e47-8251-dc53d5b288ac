import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

import { ISmtpProvider, Newsletter } from '../../domain/infrastructure/smtp-provider.interface';
import { SmtpTargetOptions } from '../../domain/models/smtp-settings/smtp-settings.abstract';

@Injectable()
export class SmtpProvider implements ISmtpProvider {
    async sendEmail(options: SmtpTargetOptions, newsLetter: Newsletter): Promise<void> {
        const client = nodemailer.createTransport(options);
        await client.sendMail(newsLetter);
        client.close();
    }

    async verifySmtpSettings(options: SmtpTargetOptions): Promise<void> {
        const client = nodemailer.createTransport({
            ...options,
            // https://www.nodemailer.com/extras/smtp-connection/
            connectionTimeout: 10000, // 10 секунд
            socketTimeout: 15000, // 15 секунд
        });
        await client.verify();
        client.close();
    }
}
