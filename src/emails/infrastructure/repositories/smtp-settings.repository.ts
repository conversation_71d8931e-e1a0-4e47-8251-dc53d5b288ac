import { Inject, Injectable } from '@nestjs/common';

import { OTEL_PRISMA_SERVICE, OtelPrismaService } from '../../../core/prisma/otel-prisma.service';
import { ISmtpSettingsRepository } from '../../domain/infrastructure/smtp-settings-repository.interface';
import { SmtpSettingsModel } from '../../domain/models/smtp-settings/smtp-settings-custom.model';

@Injectable()
export class SmtpSettingsRepository implements ISmtpSettingsRepository {
    constructor(
        @Inject(OTEL_PRISMA_SERVICE)
        private prisma: OtelPrismaService,
    ) {}

    public async findOne(schoolUuid: string): Promise<SmtpSettingsModel | null> {
        const data = await this.prisma.smtpSettings.findUnique({
            where: { schoolUuid },
        });
        if (!data) {
            return null;
        }
        const { id, ...params } = data;
        return SmtpSettingsModel.from(id, schoolUuid, params);
    }

    public async insert(model: SmtpSettingsModel): Promise<void> {
        const { id, schoolUuid, params } = model;
        await this.prisma.smtpSettings.create({
            data: { id, ...params, schoolUuid },
        });
    }

    public async update(model: SmtpSettingsModel): Promise<void> {
        await this.prisma.smtpSettings.update({
            where: { id: model.id },
            data: model.params,
        });
    }
}
