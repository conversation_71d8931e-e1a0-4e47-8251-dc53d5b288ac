import { CustomError, ERROR_CODE, Wrapper } from '@skillspace/lib';
import { isUUID } from 'class-validator';

export class SchoolUuid extends Wrapper<string> {
    constructor(value: string) {
        super(value);
    }

    public static wrap(value: string): SchoolUuid {
        return new SchoolUuid(value);
    }

    public static create(value: string): SchoolUuid {
        if (!value) {
            throw new CustomError('Идентификатор школы не определен');
        }

        if (!isUUID(value)) {
            throw new CustomError('Некорректный UUID идентификатор школы', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: { uuid: value },
            });
        }

        return new SchoolUuid(value);
    }
}
