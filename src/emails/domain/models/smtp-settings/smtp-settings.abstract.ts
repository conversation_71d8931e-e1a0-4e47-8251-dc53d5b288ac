import { SmtpSettingsParams } from './smtp-settings-custom.model';
import { SmtpSettingsDefaultParams } from './smtp-settings-default.model';

export type SmtpTargetOptions = {
    host: string;
    port: number;
    secure: boolean;
    auth: {
        user: string;
        pass: string;
    };
};

export abstract class SmtpSettingsAbstract {
    public host: string;
    public port: number;
    public user: string;
    public password: string;
    public email: string;
    public name?: string;

    protected constructor(public readonly params: SmtpSettingsParams | SmtpSettingsDefaultParams) {
        Object.assign(this, params);
    }

    public get logParams() {
        return {
            host: this.host,
            port: this.port,
            email: this.email,
        };
    }

    public abstract isDefault(): boolean;

    public getSenderAddress(fromName?: string) {
        return this.formatAddress({ fromEmail: this.email, fromName });
    }

    protected formatAddress(params: { fromEmail?: string; fromName?: string }): string {
        const name = params?.fromName;
        const email = params?.fromEmail;
        if (!name) {
            return email;
        }
        // Заключаем имя в кавычки, чтобы избежать проблем с не-ASCII символами и спецсимволами
        const safeName = `"${name.replace(/"/g, '\\"')}"`;
        return `${safeName} <${email}>`;
    }

    public get transportOptions(): SmtpTargetOptions {
        return {
            host: this.host,
            port: this.port,
            secure: this.port === 465,
            auth: {
                user: this.user,
                pass: this.password,
            },
        };
    }
}
