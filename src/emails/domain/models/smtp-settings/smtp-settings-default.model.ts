import { smtpConfig } from '../../../../config/smtp.config';
import { SmtpSettingsAbstract } from './smtp-settings.abstract';
import { SmtpSettingsParams } from './smtp-settings-custom.model';

export type SmtpSettingsDefaultParams = Omit<SmtpSettingsParams, 'schoolUuid' | 'useCustomSmtp'>;

export type SmtpSettingsUpdateParams = Partial<SmtpSettingsParams>;

export class SmtpSettingsDefaultModel extends SmtpSettingsAbstract {
    private constructor(params: SmtpSettingsDefaultParams) {
        super(params);
    }

    static create(): SmtpSettingsDefaultModel {
        return new SmtpSettingsDefaultModel(smtpConfig);
    }

    public isDefault(): boolean {
        return true;
    }
}
