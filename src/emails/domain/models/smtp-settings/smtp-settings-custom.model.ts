import { CustomError, ERROR_CODE } from '@skillspace/lib';
import { z } from 'zod';

import { smtpConfig } from '../../../../config/smtp.config';
import { Identifier } from '../../objs/identifier';
import { SchoolUuid } from '../../objs/school-uuid';
import { User } from '../user';
import { SmtpSettingsAbstract } from './smtp-settings.abstract';

const SmtpSettingsSchema = z.object({
    useCustomSmtp: z.boolean(),
    email: z.string().trim().email({ message: 'Некорректный email' }),
    user: z.string().trim().nonempty().min(1, { message: 'Поле user обязательно' }),
    host: z.string().trim().nonempty().min(1, { message: 'Поле host обязательно' }),
    port: z.number().int().min(1).max(65535, { message: 'Порт должен быть в диапазоне от 1 до 65535' }),
    password: z.string().nonempty().trim().min(1, { message: 'Поле password обязательно' }),
});

// type SmtpSettingsParams = z.infer<typeof SmtpSettingsSchema>;

export type SmtpSettingsParams = {
    useCustomSmtp: boolean;
    email: string;
    user: string;
    host: string;
    port: number;
    password: string;
};

export type SmtpSettingsUpdateParams = Partial<SmtpSettingsParams>;

export class SmtpSettingsModel extends SmtpSettingsAbstract {
    public useCustomSmtp: boolean;

    private constructor(
        public readonly id: string,
        public readonly schoolUuid: string,
        public readonly params: SmtpSettingsParams,
        public readonly isTestRequired: boolean = false,
    ) {
        super(params);
        this.useCustomSmtp = params.useCustomSmtp;
    }

    public static from(id: string, schoolUuid: string, params: SmtpSettingsParams): SmtpSettingsModel {
        return new SmtpSettingsModel(id, schoolUuid, params);
    }

    static create(anUser: User, schoolUuid: string, params: SmtpSettingsUpdateParams): SmtpSettingsModel {
        const aSchoolUuid = SchoolUuid.create(schoolUuid);
        if (!anUser.hasPermissionToModify(aSchoolUuid.unwrap())) {
            throw new CustomError('Не достаточно прав для создания smtp настроек школы', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { user: anUser.logParams },
            });
        }
        const id = Identifier.generate().unwrap();

        const createParams = {
            useCustomSmtp: params.useCustomSmtp ?? false,
            email: params.email !== undefined ? params.email.trim() : '',
            user: params.user !== undefined ? params.user.trim() : '',
            host: params.host !== undefined ? params.host.trim() : '',
            port: params.port !== undefined ? params.port : 0,
            password: params.password !== undefined ? params.password.trim() : '',
        };

        return new SmtpSettingsModel(id, aSchoolUuid.unwrap(), { ...createParams, useCustomSmtp: false }, true);
    }

    public update(anUser: User, schoolUuid: string, params: SmtpSettingsUpdateParams): SmtpSettingsModel {
        const aSchoolUuid = SchoolUuid.create(schoolUuid);
        if (!anUser.hasPermissionToModify(schoolUuid)) {
            throw new CustomError('Не достаточно прав для изменения smtp настроек школы', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: { user: anUser.logParams },
            });
        }

        const updatedParams = {
            useCustomSmtp: params.useCustomSmtp ?? this.useCustomSmtp,
            email: params.email !== undefined ? params.email.trim() : this.email,
            user: params.user !== undefined ? params.user.trim() : this.user,
            host: params.host !== undefined ? params.host.trim() : this.host,
            port: params.port !== undefined ? params.port : this.port,
            password: params.password !== undefined ? params.password.trim() : this.password,
        };

        if (params.email || params.user || params.host || params.port || params.password) {
            return new SmtpSettingsModel(this.id, aSchoolUuid.unwrap(), { ...updatedParams, useCustomSmtp: false });
        }

        if (this.useCustomSmtp === false && params.useCustomSmtp === true) {
            SmtpSettingsModel.validateSmtpSettings(updatedParams);
            return new SmtpSettingsModel(this.id, aSchoolUuid.unwrap(), updatedParams, true);
        }
        return new SmtpSettingsModel(this.id, aSchoolUuid.unwrap(), updatedParams);
    }

    static createDefault(schoolUuid: string): SmtpSettingsModel {
        const aSchoolUuid = SchoolUuid.create(schoolUuid);
        const id = Identifier.generate().unwrap();
        return new SmtpSettingsModel(id, aSchoolUuid.unwrap(), { useCustomSmtp: false, ...smtpConfig });
    }

    static validateSmtpSettings(params: SmtpSettingsParams): void {
        if (params.useCustomSmtp === false) {
            return;
        }
        const result = SmtpSettingsSchema.safeParse(params);

        if (!result.success) {
            const errorDetails = result.error.errors.map((issue) => {
                const path = issue.path.join('.');
                const message = issue.message;
                return `${path ? `${path}: ` : ''}${message}`;
            });

            throw new CustomError('Ошибка валидации SMTP настроек', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: { errors: errorDetails },
            });
        }
    }

    public isDefault(): boolean {
        return false;
    }

    public get logParams() {
        return {
            schoolUuid: this.schoolUuid,
            host: this.host,
            port: this.port,
            email: this.email,
        };
    }
}
