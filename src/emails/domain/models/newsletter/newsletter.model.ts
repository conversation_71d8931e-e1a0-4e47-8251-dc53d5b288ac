import { CustomError } from '@skillspace/lib';

import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { ITemplateEngine } from '../../infrastructure/template-engine.interface';
import { Identifier } from '../../objs/identifier';
import { createEmailTemplate, EmailTemplate } from '../email-template.factory';
import { SmtpSettingsAbstract } from '../smtp-settings/smtp-settings.abstract';

export interface NewsletterParams {
    recipients: string[];
    subject: string;
    body: TemplateParams.EmailTemplateUnion;
    template: TEMPLATE_NAME;
}

export type NewsletterToSendType = {
    subject: string;
    to: string[];
    from: string;
    html: string;
    text: string;
    locale: string;
    headers: Record<string, string>;
};

export class NewsletterModel {
    private _newsletter: NewsletterToSendType;

    private constructor(
        public readonly id: string,
        private readonly params: NewsletterParams,
        private readonly aSmtpSettings: SmtpSettingsAbstract,
        private readonly sender: { name?: string },
    ) {}

    public static create(params: NewsletterParams, smtp: SmtpSettingsAbstract, sender: { name?: string }) {
        const id = Identifier.generate().unwrap();
        return new NewsletterModel(id, params, smtp, sender);
    }

    public generate(templateEngine: ITemplateEngine) {
        const { template: templateName, body: templateData, subject, recipients: to } = this.params;

        const aEmailTemplate = createEmailTemplate(templateName, templateData);

        const html = aEmailTemplate.accept((template: EmailTemplate) => templateEngine.generateHtml(template));
        const text = aEmailTemplate.accept((template: EmailTemplate) => templateEngine.generateText(template));

        const senderAddress = this.aSmtpSettings.getSenderAddress(this.sender.name);

        const headers =
            'unSubscriberUrl' in templateData ? { 'List-Unsubscribe': `<${templateData.unSubscriberUrl}>` } : {};

        this._newsletter = {
            subject,
            to,
            from: senderAddress,
            html,
            text,
            locale: 'ru',
            headers,
        };
        return this;
    }

    public get newsletter(): NewsletterToSendType {
        if (!this._newsletter) {
            throw new CustomError('Newsletter not generated');
        }
        return this._newsletter;
    }

    public get logParams() {
        if (!this._newsletter) {
            throw new CustomError('Newsletter not generated');
        }
        return {
            subject: this._newsletter.subject,
            to: this._newsletter.to,
            from: this._newsletter.from,
            smtp: this.aSmtpSettings.logParams,
        };
    }
}
