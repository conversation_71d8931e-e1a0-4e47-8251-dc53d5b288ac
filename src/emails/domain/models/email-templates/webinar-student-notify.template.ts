import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class WebinarStudentNotifyTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'webinar_student_notify';

    public readonly lessonName: string;
    public readonly lessonLink: string;
    public readonly webinarStartAt: string | number;
    public readonly userTimeZone: string;
    public readonly timezoneAbbr: string;

    constructor(public readonly params: TemplateParams.WebinarStudentNotify) {
        super(params);
        this.lessonName = params.lessonName;
        this.lessonLink = params.lessonLink;
        this.userTimeZone = params.userTimeZone;
        this.timezoneAbbr = params.timezoneAbbr;
        this.webinarStartAt = new Intl.DateTimeFormat('ru-RU', {
            timeZone: params.userTimeZone,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
        }).format(params.webinarStartAt as number);
    }

    public accept(visitor: (template: WebinarStudentNotifyTemplate) => string): string {
        return visitor(this);
    }
}
