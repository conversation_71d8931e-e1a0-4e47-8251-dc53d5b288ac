import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class TeacherSentHomeworkAnswerCronTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'teacher_sent_homework_answer_cron';

    constructor(public readonly params: TemplateParams.TeacherSentHomeworkAnswerCron) {
        super(params);
    }

    public accept(visitor: (template: TeacherSentHomeworkAnswerCronTemplate) => string): string {
        return visitor(this);
    }
}
