import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class SchoolWithdrawRequestTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'school_withdraw_request';

    public readonly schoolName: string;
    public readonly schoolSlug: string;
    public readonly requisites: Record<string, string | number>;

    constructor(public readonly params: TemplateParams.SchoolWithdrawRequest) {
        super(params);
        this.schoolName = params.schoolName;
        this.schoolSlug = params.schoolSlug;
        this.requisites = params.requisites;
    }

    public accept(visitor: (template: SchoolWithdrawRequestTemplate) => string): string {
        return visitor(this);
    }
}
