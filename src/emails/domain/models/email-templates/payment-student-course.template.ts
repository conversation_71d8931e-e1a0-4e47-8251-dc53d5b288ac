import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class PaymentStudentCourseTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'payment_student_course';

    public readonly course: {
        id: string;
        name: string;
    };

    constructor(public readonly params: TemplateParams.PaymentStudentCourse) {
        super(params);
        this.course = params.course;
    }

    public accept(visitor: (template: PaymentStudentCourseTemplate) => string): string {
        return visitor(this);
    }
}
