import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class CourseStudentInviteAndRegisteredTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'course_student_invite_and_register';

    public readonly course?: {
        id: string;
        name: string;
    };
    public readonly categories?: {
        name: string;
    }[];
    public readonly courses?: {
        name: string;
    }[];
    public readonly changePasswordToken: string;

    constructor(public readonly params: TemplateParams.CourseStudentInviteAndRegistered) {
        super(params);
        this.course = params.course;
        this.categories = params.categories;
        this.courses = params.courses;
        this.changePasswordToken = params.changePasswordToken;
    }

    public accept(visitor: (template: CourseStudentInviteAndRegisteredTemplate) => string): string {
        return visitor(this);
    }
}
