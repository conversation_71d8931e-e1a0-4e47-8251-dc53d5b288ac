import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class UserPasswordRecoveryTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'user_password_recovery';

    public readonly password: string;

    constructor(public readonly params: TemplateParams.UserPasswordRecovery) {
        super(params);
        this.password = params.password;
    }

    public accept(visitor: (template: UserPasswordRecoveryTemplate) => string): string {
        return visitor(this);
    }
}
