import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class ExampleTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'example';

    constructor(public readonly params: TemplateParams.Example) {
        super(params);
    }

    public accept(visitor: (template: ExampleTemplate) => string): string {
        return visitor(this);
    }
}
