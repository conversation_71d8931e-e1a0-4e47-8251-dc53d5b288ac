import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class TariffEnds1DayTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'tariff_ends_1_day';

    constructor(public readonly params: TemplateParams.TariffEnds1Day) {
        super(params);
    }

    public accept(visitor: (template: TariffEnds1DayTemplate) => string): string {
        return visitor(this);
    }
}
