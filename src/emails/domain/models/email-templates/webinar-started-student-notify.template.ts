import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class WebinarStartedStudentNotifyTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'webinar_started_student_notify';

    public readonly lessonName: string;
    public readonly lessonLink: string;

    constructor(public readonly params: TemplateParams.WebinarStudentNotify) {
        super(params);
        this.lessonName = params.lessonName;
        this.lessonLink = params.lessonLink;
    }

    public accept(visitor: (template: WebinarStartedStudentNotifyTemplate) => string): string {
        return visitor(this);
    }
}
