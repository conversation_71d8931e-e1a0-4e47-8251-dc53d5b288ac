import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class UserChangeEmailSuccessTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'user_change_email_success';

    public readonly newEmail: string;

    constructor(public readonly params: TemplateParams.UserChangeEmailSuccess) {
        super(params);
        this.newEmail = params.newEmail;
    }

    public accept(visitor: (template: UserChangeEmailSuccessTemplate) => string): string {
        return visitor(this);
    }
}
