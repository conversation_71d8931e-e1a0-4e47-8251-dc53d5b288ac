import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class TestSmtpTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'test_smtp';

    constructor(public readonly params: TemplateParams.TestSmtp) {
        super(params);
    }

    public accept(visitor: (template: TestSmtpTemplate) => string): string {
        return visitor(this);
    }
}
