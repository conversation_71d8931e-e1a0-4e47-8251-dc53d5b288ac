import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class CourseFinishedTeacherNotifyTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'course_finished_teacher_notify';

    public readonly courses: {
        name: string;
        students: {
            name: string;
            email: string;
        }[];
    }[];

    constructor(public readonly params: TemplateParams.CourseFinishedTeacherNotify) {
        super(params);
        this.courses = params.courses;
    }

    public accept(visitor: (template: CourseFinishedTeacherNotifyTemplate) => string): string {
        return visitor(this);
    }
}
