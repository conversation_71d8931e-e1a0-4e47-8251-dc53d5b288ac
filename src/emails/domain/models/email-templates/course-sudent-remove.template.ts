import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class CourseStudentRemoveTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'course_student_remove';

    public readonly schoolName: string;
    public readonly course: {
        name: string;
    };

    constructor(public readonly params: TemplateParams.CourseStudentRemove) {
        super(params);
        this.schoolName = params.schoolName;
        this.course = params.course;
    }

    public accept(visitor: (template: CourseStudentRemoveTemplate) => string): string {
        return visitor(this);
    }
}
