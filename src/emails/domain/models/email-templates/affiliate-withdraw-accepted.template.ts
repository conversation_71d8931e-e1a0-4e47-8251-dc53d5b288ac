import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class AffiliateWithdrawAcceptedTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'affiliate_withdraw_accepted';

    constructor(public readonly params: TemplateParams.UnreadMessageYet) {
        super(params);
    }

    public accept(visitor: (template: AffiliateWithdrawAcceptedTemplate) => string): string {
        return visitor(this);
    }
}
