import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';

export class SchoolAppendRequisitesTemplate {
    public readonly template: TEMPLATE_NAME = 'school_append_requisite';

    public readonly schoolName: string;
    public readonly requisiteId: string;

    constructor(public readonly params: TemplateParams.SchoolAppendRequisites) {
        this.schoolName = params.schoolName;
        this.requisiteId = params.requisiteId;
    }

    public accept(visitor: (template: SchoolAppendRequisitesTemplate) => string): string {
        return visitor(this);
    }
}
