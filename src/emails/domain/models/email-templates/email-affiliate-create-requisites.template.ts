import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class EmailAffiliateCreateRequisitesTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'email_affiliate_create_requisites';

    public readonly partnerUser: {
        email: string;
    };

    constructor(public readonly params: TemplateParams.EmailAffiliateCreateRequisites) {
        super(params);
        this.partnerUser = params.partnerUser;
    }

    public accept(visitor: (template: EmailAffiliateCreateRequisitesTemplate) => string): string {
        return visitor(this);
    }
}
