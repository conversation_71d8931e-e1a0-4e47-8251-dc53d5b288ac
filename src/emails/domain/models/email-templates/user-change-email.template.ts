import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class UserChangeEmailTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'user_change_email';

    public readonly changeUrl: string;

    constructor(public readonly params: TemplateParams.UserChangeEmail) {
        super(params);
        this.changeUrl = params.changeUrl;
    }

    public accept(visitor: (template: UserChangeEmailTemplate) => string): string {
        return visitor(this);
    }
}
