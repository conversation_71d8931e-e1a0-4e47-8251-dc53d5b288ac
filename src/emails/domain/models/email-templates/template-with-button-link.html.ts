import { TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class TemplateWithButtonLinkHTML extends TemplateHTML {
    public readonly schoolDomain: string;
    public readonly schoolSlug: string;

    constructor(public readonly params: TemplateParams.TemplateWithButtonLink) {
        super(params);
        this.schoolDomain = params?.schoolDomain;
        this.schoolSlug = params.schoolSlug;
    }

    public accept(visitor: (template: TemplateWithButtonLinkHTML) => string): string {
        return visitor(this);
    }
}
