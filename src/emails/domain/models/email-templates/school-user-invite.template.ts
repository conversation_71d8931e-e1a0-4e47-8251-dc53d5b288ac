import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class SchoolUserInviteTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'school_user_invite';

    public readonly schoolName: string;
    public readonly subject: string;

    constructor(public readonly params: TemplateParams.SchoolUserInvite) {
        super(params);
        this.schoolName = params.schoolName;
        this.subject = params.subject;
    }

    public accept(visitor: (template: SchoolUserInviteTemplate) => string): string {
        return visitor(this);
    }
}
