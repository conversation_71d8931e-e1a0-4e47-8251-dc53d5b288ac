import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class UserPasswordChangeTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'user_password_change';

    constructor(public readonly params: TemplateParams.UserPasswordChange) {
        super(params);
    }

    public accept(visitor: (template: UserPasswordChangeTemplate) => string): string {
        return visitor(this);
    }
}
