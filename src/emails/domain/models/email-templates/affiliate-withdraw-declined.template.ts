import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class AffiliateWithdrawDeclinedTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'affiliate_withdraw_declined';

    constructor(public readonly params: TemplateParams.UnreadMessageYet) {
        super(params);
    }

    public accept(visitor: (template: AffiliateWithdrawDeclinedTemplate) => string): string {
        return visitor(this);
    }
}
