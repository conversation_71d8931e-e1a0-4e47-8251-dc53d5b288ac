import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class CourseStudentBoughtTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'course_student_bought';

    public readonly course: {
        id: string;
    };

    constructor(public readonly params: TemplateParams.CourseStudentBought) {
        super(params);
        this.course = params.course;
    }

    public accept(visitor: (template: CourseStudentBoughtTemplate) => string): string {
        return visitor(this);
    }
}
