import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class SchoolUserInviteAndRegisterTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'school_user_invite_and_register';

    public readonly schoolName: string;
    public readonly subject: string;
    public readonly changePasswordToken: string;

    constructor(public readonly params: TemplateParams.SchoolUserInviteAndRegister) {
        super(params);
        this.schoolName = params.schoolName;
        this.subject = params.subject;
        this.changePasswordToken = params.changePasswordToken;
    }

    public accept(visitor: (template: SchoolUserInviteAndRegisterTemplate) => string): string {
        return visitor(this);
    }
}
