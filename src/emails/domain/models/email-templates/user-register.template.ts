import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class UserRegisterTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'user_register';

    public readonly changePasswordToken: string;

    constructor(public readonly params: TemplateParams.UserRegister) {
        super(params);
        this.changePasswordToken = params.changePasswordToken;
    }

    public accept(visitor: (template: UserRegisterTemplate) => string): string {
        return visitor(this);
    }
}
