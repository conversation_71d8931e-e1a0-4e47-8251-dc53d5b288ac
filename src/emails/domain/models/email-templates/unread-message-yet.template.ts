import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class UnreadMessageYetTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'unread_messages_yet';

    constructor(public readonly params: TemplateParams.UnreadMessageYet) {
        super(params);
    }

    public accept(visitor: (template: UnreadMessageYetTemplate) => string): string {
        return visitor(this);
    }
}
