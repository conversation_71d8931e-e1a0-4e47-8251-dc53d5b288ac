import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class PaymentSchoolPlanTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'payment_school_plan';

    public readonly plan: {
        name: string;
        endAt: number | string;
    };

    constructor(public readonly params: TemplateParams.PaymentSchoolPlan) {
        super(params);
        this.plan = params.plan;
    }

    public accept(visitor: (template: PaymentSchoolPlanTemplate) => string): string {
        return visitor(this);
    }
}
