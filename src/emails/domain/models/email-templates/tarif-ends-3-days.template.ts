import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class TariffEnds3DaysTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'tariff_ends_3_days';

    constructor(public readonly params: TemplateParams.TariffEnds3Day) {
        super(params);
    }

    public accept(visitor: (template: TariffEnds3DaysTemplate) => string): string {
        return visitor(this);
    }
}
