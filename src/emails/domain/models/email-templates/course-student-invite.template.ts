import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class CourseStudentInviteTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'course_student_invite';

    public readonly course?: {
        id: number | string;
        name: string;
    };
    public readonly categories?: {
        name: string;
    }[];
    public readonly courses?: {
        name: string;
    }[];

    constructor(public readonly params: TemplateParams.CourseStudentInvite) {
        super(params);
        this.course = params.course;
        this.categories = params.categories;
        this.courses = params.courses;
    }

    public accept(visitor: (template: CourseStudentInviteTemplate) => string): string {
        return visitor(this);
    }
}
