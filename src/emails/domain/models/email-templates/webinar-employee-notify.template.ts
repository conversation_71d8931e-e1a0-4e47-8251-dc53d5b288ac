import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class WebinarEmployeeNotifyTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'webinar_employee_notify';

    public readonly lessonName: string;
    public readonly webinarStreamLink: string;
    public readonly webinarStartAt: string;
    public readonly userTimeZone: string;
    public readonly timezoneAbbr: string;

    constructor(public readonly params: TemplateParams.WebinarEmployeeNotify) {
        super(params);
        this.lessonName = params.lessonName;
        this.webinarStreamLink = params.webinarStreamLink;
        this.userTimeZone = params.userTimeZone;
        this.timezoneAbbr = params.timezoneAbbr;
        this.webinarStartAt = new Intl.DateTimeFormat('ru-RU', {
            timeZone: params.userTimeZone,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
        }).format(params.webinarStartAt as number);
    }

    public accept(visitor: (template: WebinarEmployeeNotifyTemplate) => string): string {
        return visitor(this);
    }
}
