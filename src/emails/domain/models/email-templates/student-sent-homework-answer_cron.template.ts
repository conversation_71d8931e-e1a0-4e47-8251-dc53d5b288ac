import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class StudentSentHomeworkAnswerCronTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'student_sent_homework_answer_cron';

    constructor(public readonly params: TemplateParams.StudentSentHomeworkAnswerCron) {
        super(params);
    }

    public accept(visitor: (template: StudentSentHomeworkAnswerCronTemplate) => string): string {
        return visitor(this);
    }
}
