import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class EmailAffiliateRequestWithdrawTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'email_affiliate_request_withdraw';

    public readonly partnerUser: {
        id: string;
        email: string;
    };

    constructor(public readonly params: TemplateParams.EmailAffiliatePartnerWithdraw) {
        super(params);
        this.partnerUser = params.partnerUser;
    }

    public accept(visitor: (template: EmailAffiliateRequestWithdrawTemplate) => string): string {
        return visitor(this);
    }
}
