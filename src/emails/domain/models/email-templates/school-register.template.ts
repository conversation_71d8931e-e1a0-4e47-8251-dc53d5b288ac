import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class SchoolRegisterTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'school_register';

    constructor(public readonly params: TemplateParams.SchoolRegister) {
        super(params);
    }

    public accept(visitor: (template: SchoolRegisterTemplate) => string): string {
        return visitor(this);
    }
}
