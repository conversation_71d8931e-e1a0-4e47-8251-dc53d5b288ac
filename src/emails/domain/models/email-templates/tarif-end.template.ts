import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class TariffEndTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'tariff_end';

    constructor(public readonly params: TemplateParams.TariffEnd) {
        super(params);
    }

    public accept(visitor: (template: TariffEndTemplate) => string): string {
        return visitor(this);
    }
}
