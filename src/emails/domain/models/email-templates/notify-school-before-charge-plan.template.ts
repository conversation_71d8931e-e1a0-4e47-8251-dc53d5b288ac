import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class NotifySchoolBeforeChargePlanTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'notify_school_before_charge_plan';

    public readonly plan: {
        name: string;
        endAt: string | number;
    };

    constructor(public readonly params: TemplateParams.NotifySchoolBeforeChargePlan) {
        super(params);
        this.plan = params.plan;
    }

    public accept(visitor: (template: NotifySchoolBeforeChargePlanTemplate) => string): string {
        return visitor(this);
    }
}
