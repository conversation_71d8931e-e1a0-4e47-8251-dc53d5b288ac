import { TemplateParams } from '../../../../contracts';

export class TemplateHTML {
    public readonly textOnly: boolean;
    public readonly unSubscriberUrl: string;
    public readonly schoolLogoUrl: string;
    public readonly hideMobileAppLinks: boolean;
    public readonly isWhiteLabel: boolean;
    public readonly primaryColor: string;

    constructor(public readonly params: TemplateParams.Template) {
        this.textOnly = params?.textOnly;
        this.unSubscriberUrl = params?.unSubscriberUrl;
        this.schoolLogoUrl = params?.schoolLogoUrl;
        this.hideMobileAppLinks = params?.hideMobileAppLinks;
        this.isWhiteLabel = params?.isWhiteLabel;
        this.primaryColor = params?.primaryColor;
    }

    public accept(visitor: (template: TemplateHTML) => string): string {
        return visitor(this);
    }
}
