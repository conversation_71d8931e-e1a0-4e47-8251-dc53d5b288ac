import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateWithButtonLinkHTML } from './template-with-button-link.html';

export class TariffEnds7DayTemplate extends TemplateWithButtonLinkHTML {
    public readonly template: TEMPLATE_NAME = 'tariff_ends_7_days';

    constructor(public readonly params: TemplateParams.TariffEnds7Day) {
        super(params);
    }

    public accept(visitor: (template: TariffEnds7DayTemplate) => string): string {
        return visitor(this);
    }
}
