import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';

export class EmailAffiliatePartnerRegisterTemplate {
    public readonly template: TEMPLATE_NAME = 'email_affiliate_partner_register';

    public readonly partnerProgramLink: string;
    public readonly unSubscriberUrl: string;

    constructor(public readonly params: TemplateParams.EmailAffiliatePartnerRegistered) {
        this.partnerProgramLink = params.partnerProgramLink;
        this.unSubscriberUrl = params.unSubscriberUrl;
    }

    public accept(visitor: (template: EmailAffiliatePartnerRegisterTemplate) => string): string {
        return visitor(this);
    }
}
