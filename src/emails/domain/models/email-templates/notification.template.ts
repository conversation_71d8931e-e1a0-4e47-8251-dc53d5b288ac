import { TEMPLATE_NAME, TemplateParams } from '../../../../contracts';
import { TemplateHTML } from './template.html';

export class NotificationTemplate extends TemplateHTML {
    public readonly template: TEMPLATE_NAME = 'notification';

    public readonly title: string;
    public readonly html: string;
    public readonly buttonText: string;
    public readonly redirectUrl: string;

    constructor(public readonly params: TemplateParams.Notification) {
        super(params);
        this.title = params?.title;
        this.html = params.html;
        this.buttonText = params?.buttonText;
        this.redirectUrl = params?.redirectUrl;
    }

    public accept(visitor: (template: NotificationTemplate) => string): string {
        return visitor(this);
    }
}
