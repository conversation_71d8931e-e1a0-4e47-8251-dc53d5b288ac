import { CustomError, ERROR_CODE } from '@skillspace/lib';

import { TEMPLATE_NAME } from '../../../contracts';
import { TemplateParams } from '../../../contracts';
import { AffiliateWithdrawAcceptedTemplate } from './email-templates/affiliate-withdraw-accepted.template';
import { AffiliateWithdrawDeclinedTemplate } from './email-templates/affiliate-withdraw-declined.template';
import { CourseStudentBoughtTemplate } from './email-templates/course-bougth.template';
import { CourseFinishedTeacherNotifyTemplate } from './email-templates/course-finished-teacher-notify.template';
import { CourseStudentInviteTemplate } from './email-templates/course-student-invite.template';
import { CourseStudentInviteAndRegisteredTemplate } from './email-templates/course-sudent-invite-and-registered.template';
import { CourseStudentRemoveTemplate } from './email-templates/course-sudent-remove.template';
import { EmailAffiliateCreateRequisitesTemplate } from './email-templates/email-affiliate-create-requisites.template';
import { EmailAffiliatePartnerRegisterTemplate } from './email-templates/email-affiliate-partner-register.template';
import { EmailAffiliateRequestWithdrawTemplate } from './email-templates/email-affiliate-request-withdraw.template';
import { ExampleTemplate } from './email-templates/example.template';
import { NotificationTemplate } from './email-templates/notification.template';
import { NotifySchoolBeforeChargePlanTemplate } from './email-templates/notify-school-before-charge-plan.template';
import { PaymentSchoolPlanTemplate } from './email-templates/payment-school-plan.template';
import { PaymentStudentCourseTemplate } from './email-templates/payment-student-course.template';
import { SchoolAppendRequisitesTemplate } from './email-templates/school-append-requisite.template';
import { SchoolRegisterTemplate } from './email-templates/school-register.template';
import { SchoolUserInviteTemplate } from './email-templates/school-user-invite.template';
import { SchoolUserInviteAndRegisterTemplate } from './email-templates/school-user-invite-and-register.template';
import { SchoolWithdrawRequestTemplate } from './email-templates/school-withdraw-request.template';
import { StudentSentHomeworkAnswerCronTemplate } from './email-templates/student-sent-homework-answer_cron.template';
import { TariffEndTemplate } from './email-templates/tarif-end.template';
import { TariffEnds1DayTemplate } from './email-templates/tarif-ends-1-day.template';
import { TariffEnds3DaysTemplate } from './email-templates/tarif-ends-3-days.template';
import { TariffEnds7DayTemplate } from './email-templates/tarif-ends-7-days.template';
import { TeacherSentHomeworkAnswerTemplate } from './email-templates/teacher-sent-homework-answer.template';
import { TeacherSentHomeworkAnswerCronTemplate } from './email-templates/teacher-sent-homework-answer-cron.template';
import { TestSmtpTemplate } from './email-templates/test-smtp.template';
import { UnreadMessageYetTemplate } from './email-templates/unread-message-yet.template';
import { UserChangeEmailTemplate } from './email-templates/user-change-email.template';
import { UserChangeEmailSuccessTemplate } from './email-templates/user-change-email-success.template';
import { UserPasswordChangeTemplate } from './email-templates/user-password-change.template';
import { UserPasswordRecoveryTemplate } from './email-templates/user-password-recovery.template';
import { UserRegisterTemplate } from './email-templates/user-register.template';
import { WebinarEmployeeNotifyTemplate } from './email-templates/webinar-employee-notify.template';
import { WebinarStartedStudentNotifyTemplate } from './email-templates/webinar-started-student-notify.template';
import { WebinarStudentNotifyTemplate } from './email-templates/webinar-student-notify.template';

export type EmailTemplate =
    | AffiliateWithdrawAcceptedTemplate
    | AffiliateWithdrawDeclinedTemplate
    | CourseFinishedTeacherNotifyTemplate
    | CourseStudentBoughtTemplate
    | CourseStudentInviteTemplate
    | CourseStudentInviteAndRegisteredTemplate
    | CourseStudentRemoveTemplate
    | EmailAffiliateCreateRequisitesTemplate
    | EmailAffiliatePartnerRegisterTemplate
    | EmailAffiliateRequestWithdrawTemplate
    | ExampleTemplate
    | NotificationTemplate
    | NotifySchoolBeforeChargePlanTemplate
    | PaymentSchoolPlanTemplate
    | PaymentStudentCourseTemplate
    | SchoolAppendRequisitesTemplate
    | SchoolRegisterTemplate
    | SchoolUserInviteTemplate
    | SchoolUserInviteAndRegisterTemplate
    | SchoolWithdrawRequestTemplate
    | TariffEndTemplate
    | TariffEnds1DayTemplate
    | TariffEnds3DaysTemplate
    | TariffEnds7DayTemplate
    | TeacherSentHomeworkAnswerTemplate
    | TeacherSentHomeworkAnswerCronTemplate
    | TestSmtpTemplate
    | UnreadMessageYetTemplate
    | UserChangeEmailTemplate
    | UserChangeEmailSuccessTemplate
    | UserPasswordChangeTemplate
    | UserPasswordRecoveryTemplate
    | UserRegisterTemplate
    | WebinarEmployeeNotifyTemplate
    | WebinarStudentNotifyTemplate
    | WebinarStartedStudentNotifyTemplate;

const emailTemplatesMap: Record<TEMPLATE_NAME, any> = {
    affiliate_withdraw_accepted: AffiliateWithdrawAcceptedTemplate,
    affiliate_withdraw_declined: AffiliateWithdrawDeclinedTemplate,
    course_finished_teacher_notify: CourseFinishedTeacherNotifyTemplate,
    course_student_bought: CourseStudentBoughtTemplate,
    course_student_invite: CourseStudentInviteTemplate,
    course_student_invite_and_register: CourseStudentInviteAndRegisteredTemplate,
    course_student_remove: CourseStudentRemoveTemplate,
    email_affiliate_create_requisites: EmailAffiliateCreateRequisitesTemplate,
    email_affiliate_partner_register: EmailAffiliatePartnerRegisterTemplate,
    email_affiliate_request_withdraw: EmailAffiliateRequestWithdrawTemplate,
    example: ExampleTemplate,
    notification: NotificationTemplate,
    notify_school_before_charge_plan: NotifySchoolBeforeChargePlanTemplate,
    payment_school_plan: PaymentSchoolPlanTemplate,
    payment_student_course: PaymentStudentCourseTemplate,
    school_append_requisite: SchoolAppendRequisitesTemplate,
    school_register: SchoolRegisterTemplate,
    school_user_invite: SchoolUserInviteTemplate,
    school_user_invite_and_register: SchoolUserInviteAndRegisterTemplate,
    school_withdraw_request: SchoolWithdrawRequestTemplate,
    student_sent_homework_answer_cron: StudentSentHomeworkAnswerCronTemplate,
    tariff_end: TariffEndTemplate,
    tariff_ends_1_day: TariffEnds1DayTemplate,
    tariff_ends_3_days: TariffEnds3DaysTemplate,
    tariff_ends_7_days: TariffEnds7DayTemplate,
    teacher_sent_homework_answer_cron: TeacherSentHomeworkAnswerCronTemplate,
    test_smtp: TestSmtpTemplate,
    unread_messages_yet: UnreadMessageYetTemplate,
    user_change_email: UserChangeEmailTemplate,
    user_change_email_success: UserChangeEmailSuccessTemplate,
    user_password_change: UserPasswordChangeTemplate,
    user_password_recovery: UserPasswordRecoveryTemplate,
    user_register: UserRegisterTemplate,
    webinar_employee_notify: WebinarEmployeeNotifyTemplate,
    webinar_student_notify: WebinarStudentNotifyTemplate,
    webinar_started_student_notify: WebinarStartedStudentNotifyTemplate,
};

export function createEmailTemplate(
    templateName: TEMPLATE_NAME,
    params: TemplateParams.EmailTemplateUnion,
): EmailTemplate {
    const EmailTemplateClass = emailTemplatesMap[templateName];
    if (!EmailTemplateClass) {
        throw new CustomError(`Шаблон с кодом ${templateName} не найден`, { code: ERROR_CODE.INTERNAL_ERROR });
    }
    return new EmailTemplateClass(params);
}
