import { EmailsUserContext } from '../../../shared/lib/user-context.type';
import { SchoolUuid } from '../objs/school-uuid';

export const SMTP_MODIFY_PERMISSION = 'ACTION_SCHOOL_WHITE_LABEL_MODIFY';

export type UserParams = EmailsUserContext & {
    schoolUuid: string;
};

export class User {
    private readonly schoolUuid: string;

    private constructor(private readonly params: UserParams) {
        this.schoolUuid = SchoolUuid.create(params.schoolUuid).unwrap();
    }

    static create(input: UserParams): User {
        return new User(input);
    }

    public hasPermissionToModify(schoolUuid: string): boolean {
        return (
            this.schoolUuid === schoolUuid &&
            this.params.role === 'ROLE_EMPLOYEE' &&
            this.params.actions.includes(SMTP_MODIFY_PERMISSION)
        );
    }

    public hasPermissionToRead(schoolUuid: string): boolean {
        return this.hasPermissionToModify(schoolUuid);
    }

    public logParams() {
        return {
            user: this.params,
            schoolUuid: this.schoolUuid,
        };
    }
}
