import { SmtpTargetOptions } from '../models/smtp-settings/smtp-settings.abstract';

export interface Newsletter {
    from: string;
    to: string[];
    subject: string;
    html: string;
    text: string;
    locale: string;
    headers: Record<string, string>;
}

export interface ISmtpProvider {
    sendEmail(options: SmtpTargetOptions, newsLetter: Newsletter): Promise<void>;
    verifySmtpSettings(options: SmtpTargetOptions): Promise<void>;
}
