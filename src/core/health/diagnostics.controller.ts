import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService } from '@nestjs/terminus';
import { OtelCollectorIndicator } from '@skillspace/lib';

@Controller('diagnostics')
export class DiagnosticsController {
    constructor(
        private health: HealthCheckService,
        private collectorHealth: OtelCollectorIndicator,
    ) {}

    @Get()
    @HealthCheck()
    check() {
        return this.health.check([async () => this.collectorHealth.pingCheck('tracing')]);
    }
}
