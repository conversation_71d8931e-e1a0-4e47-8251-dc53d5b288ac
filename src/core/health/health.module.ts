import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { OtelCollectorIndicator } from '@skillspace/lib';

import { PrismaModule } from '../prisma/prisma.module';
import { DiagnosticsController } from './diagnostics.controller';
import { HealthController } from './health.controller';

@Module({
    controllers: [HealthController, DiagnosticsController],
    imports: [
        PrismaModule,
        TerminusModule.forRoot({
            errorLogStyle: 'pretty',
        }),
    ],
    providers: [OtelCollectorIndicator],
    exports: [OtelCollectorIndicator],
})
export class HealthModule {}
