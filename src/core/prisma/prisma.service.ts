import { INestApplication, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
    logger = new Logger(PrismaService.name);
    constructor() {
        super();
    }

    async onModuleInit() {
        try {
            await this.$connect();
            this.logger.verbose('Successfully connected to MongoDB Emails service');
        } catch (err) {
            this.logger.error('Unsuccessfully connected to MongoDB Emails service', err);
        }
    }

    async enableShutdownHooks(app: INestApplication) {
        // @ts-ignore
        this.$on('beforeExit', async () => {
            await app.close();
        });
    }
}
