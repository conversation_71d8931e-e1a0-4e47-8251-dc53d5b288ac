import { resolveInt, resolveStr } from './utils';

export const enum NODE_ENV {
    DEV = 'development',
    PROD = 'production',
    TEST = 'test',
}

if (process.env.NODE_ENV !== NODE_ENV.PROD) {
    resolveInt('DB_PORT', process.env.DB_PORT);
}

interface AppConfig {
    port: number;
    nodeEnv: string;
    smtpConsoleOutputEnable: boolean;
}

export const appConfig: AppConfig = {
    port: resolveInt('APP_PORT', process.env.APP_PORT),
    nodeEnv: resolveStr('NODE_ENV', process.env.NODE_ENV),
    smtpConsoleOutputEnable: process.env.SMTP_CONSOLE_OUTPUT === 'true' || false,
};
