import { resolveInt, resolveStr } from './utils';

export type SmtpConfig = {
    host: string;
    port: number;
    user: string;
    password: string;
    email: string;
    name: string;
};

export const smtpConfig: SmtpConfig = {
    host: resolveStr('SMTP_HOST', process.env.SMTP_HOST),
    port: resolveInt('SMTP_PORT', process.env.SMTP_PORT),
    user: resolveStr('SMTP_USER', process.env.SMTP_USER),
    password: resolveStr('SMTP_PASSWORD', process.env.SMTP_PASSWORD),
    email: resolveStr('SMTP_EMAIL', process.env.SMTP_EMAIL),
    name: resolveStr('SMTP_NAME', process.env.SMTP_NAME),
};
