generator client {
  provider        = "prisma-client-js"
  binaryTargets   = ["native", "linux-musl", "linux-musl-arm64-openssl-1.1.x", "linux-musl-openssl-3.0.x", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model SmtpSettings {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  schoolUuid    String  @unique
  useCustomSmtp Boolean

  host     String
  port     Int
  user     String
  password String
  email    String

  @@map("smtp_settings")
}
