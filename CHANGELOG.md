# v1.2.3

#### 02.04.2025

- Обновил либы

# v1.2.2

#### 25.02.2025

- Обновил шаблон уведомлений
- Обновил код с использованием библиотеки
- Исправил отправку писем с дефолтными настройками (исключил подмену email)

# v1.2.0

#### 14.02.2025

- добавлен резолвер для SMTP
- добавлена проверка SMTP, отправка тестового письма
- проведен рефакторинг кода предметной области
- обновлена обработка ошибок и логирование

# v1.0.18

#### 12.02.2025

- pnpm

# v1.0.17

#### 28.01.2025

1. Обновил пакеты логирования и трассировки
2. Разделил роуты для проверки сервиса

# v1.0.16

#### 25.12.2024

1. Фикс призмы

# v1.0.15

#### 20.12.2024

1. Фикс ерсии призма

# v1.0.14

#### 20.12.2024

1. Фикс заголовка from для соответствия RFC 5322

# v1.0.13

#### 17.12.2024

1. Prisma до последней версии

# v1.0.12

#### 17.12.2024

1. Добавил параметр isCustomSTMP в логи, чтобы проще разбираться в ошибках

# v1.0.11

#### 25.11.2024

1. Убрал использование exception filter

# v1.0.10

#### 25.11.2024

1. secure: true для 465, удалил authMethod

# v1.0.9

#### 25.11.2024

1. Удалил тесты

# v1.0.8

#### 25.11.2024

1. делаю secure true для всех портов кроме 25

# v1.0.7

#### 22.11.2024

1. CI/CD

# v1.0.6

#### 21.11.2024

1. Исправил настройки для защищенного smtp соединения

# v1.0.5

#### 21.11.2024

1. Фиксы ci/cd

# v1.0.4

#### 21.11.2024

1. Фикс старта приложения в продакшн

# v1.0.3

#### 21.11.2024

1. Copyfiles перенесен в dep

# v1.0.2

#### 21.11.2024

1. Снес тесты

# v1.0.1

#### 21.11.2024

1. Фиксы ci/cd

# v1.0.0

#### 21.11.2024

1. Первая стабильная версия

# v0.0.1

#### 15.10.2024

1. Первая реализация сервиса рассылки писем
