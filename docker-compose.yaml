services:
  app:
    build:
      context: .
      dockerfile: docker/${NODE_ENV}.dockerfile
    container_name: emails-service-app
    hostname: emails-service-app
    volumes:
      - ./:/app
    ports:
      - ${APP_PORT}:${APP_PORT}
    depends_on:
      - mongo
    networks:
      - skillspace-network
    env_file:
      - ./.env

  mongo:
    image: mongo:7.0
    container_name: emails-service-db
    hostname: emails-service-db
    command: ['--replSet', 'rs0', '--bind_ip_all']
    volumes:
      - .data/mongo:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js
    networks:
      - skillspace-network
    ports:
      - ${DB_PORT}:27017

networks:
  skillspace-network:
    external: true
