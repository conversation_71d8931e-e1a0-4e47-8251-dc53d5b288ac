module.exports = {
    moduleFileExtensions: ['js', 'json', 'ts'],
    rootDir: '../',
    modulePaths: ['.'],
    testRegex: '.*\\.spec\\.ts$',
    transform: {
        '^.+\\.(t|j)s$': 'ts-jest',
    },
    collectCoverageFrom: ['<rootDir>/**/*.(t|j)s'],
    coveragePathIgnorePatterns: ['<rootDir>/tests'],
    coverageDirectory: '<rootDir>/tests/coverage',
    testEnvironment: 'node',
    globalSetup: '<rootDir>/tests/config/jest.setup.ts',
    globalTeardown: '<rootDir>/tests/config/jest.teardown.ts',
    workerIdleMemoryLimit: 0.2,
    watchPathIgnorePatterns: [
        '!<rootDir>/src/',
        '!<rootDir>/tests/',
        '<rootDir>/.data/',
    ],
};
