import 'dotenv/config';

import { LogLevel } from '@skillspace/lib';
import { RabbitMQContainer } from '@testcontainers/rabbitmq';
import { MongoMemoryReplSet } from 'mongodb-memory-server';

import { DEFAULT_SMTP } from '../integration/test-constants';

module.exports = async () => {
    const replSet = await MongoMemoryReplSet.create({ replSet: { count: 1 } });
    await replSet.waitUntilRunning();

    const uri: string = replSet.getUri('emails');
    console.log(`Database URI: ${uri}`);

    const rabbitMqContainer = await new RabbitMQContainer('rabbitmq:3.9-management').withReuse().start();

    process.env.RMQ_URL = rabbitMqContainer.getAmqpUrl();
    process.env.DATABASE_URL = uri;
    process.env.LOG_LEVEL = LogLevel.Silent;

    process.env.SMTP_HOST = DEFAULT_SMTP.host;
    process.env.SMTP_PORT = DEFAULT_SMTP.port;
    process.env.SMTP_USER = DEFAULT_SMTP.user;
    process.env.SMTP_PASSWORD = DEFAULT_SMTP.password;
    process.env.SMTP_EMAIL = DEFAULT_SMTP.email;
    process.env.SMTP_NAME = DEFAULT_SMTP.name;

    global.__MONGO_SERVER__ = replSet;
};
