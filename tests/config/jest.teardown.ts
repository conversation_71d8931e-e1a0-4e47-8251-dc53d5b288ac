module.exports = async () => {
    //                                   # bytes |  KB  | MB   | GB
    const gbNow = process.memoryUsage().heapUsed / 1024 / 1024 / 1024;
    const gbRounded = Math.round(gbNow * 100) / 100;
    console.log(`<PERSON>ap allocated ${gbRounded} GB`);

    if (global.__MONGO_SERVER__) {
        await global.__MONGO_SERVER__.stop();
    }

    if (global.__RABBITMQ_CONTAINER__) {
        await global.__RABBITMQ_CONTAINER__.stop();
    }
    process.exit(0);
};
