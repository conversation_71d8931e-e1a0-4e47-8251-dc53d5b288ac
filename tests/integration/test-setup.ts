import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { PrismaClient } from '@prisma/client';
import { createLogger } from '@skillspace/lib';

import { AppModule } from '../../src/app.module';
import { setupGlobalMiddlewares } from '../../src/bootstrap-setup';
import { SMTP_PROVIDER } from '../../src/emails/injects';
import { SmtpSettingsResolver } from '../../src/emails/presentation/smtp-settings.resolver';
import * as schoolSettingsData from './__data__/smtp-settings.data.json';
import { SmtpProviderMock } from './__mock__/smtp-provider.mock';
import { EMPLOYEE_01, EMPLOYEE_02 } from './test-constants';
import { createGqlTestSession, GqlTestSession } from './user-session';

let app: INestApplication;
let prisma: PrismaClient;

let smtpSettingsResolver: SmtpSettingsResolver;

let employeeSession01: GqlTestSession;
let employeeSession02: GqlTestSession;

beforeAll(async () => {
    const builder = Test.createTestingModule({
        imports: [AppModule],
    });

    const moduleFixture = await builder.overrideProvider(SMTP_PROVIDER).useClass(SmtpProviderMock).compile();
    app = moduleFixture.createNestApplication({ logger: await createLogger() });

    setupGlobalMiddlewares(app);

    await app.init();

    employeeSession01 = await createGqlTestSession(app, EMPLOYEE_01);
    employeeSession02 = await createGqlTestSession(app, EMPLOYEE_02);

    prisma = new PrismaClient();
    await prisma.$connect();
});

beforeEach(async () => {
    jest.clearAllMocks();
    await prisma.smtpSettings.createMany({
        data: schoolSettingsData,
    });
});

afterEach(async () => {
    await prisma.smtpSettings.deleteMany();
});

afterAll(async () => {
    await prisma.$disconnect();
    await app.close();
});

export { app, employeeSession01, employeeSession02, prisma, smtpSettingsResolver };
