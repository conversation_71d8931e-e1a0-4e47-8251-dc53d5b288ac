import { ISmtpProvider } from '../../../src/emails/domain/infrastructure/smtp-provider.interface';

export class SmtpProviderMock implements ISmtpProvider {
    async sendEmail(): Promise<void> {
        return;
    }

    // async sendEmail(options: SmtpTargetOptions, newsLetter: Newsletter): Promise<void> {
    //     console.log({ options, newsLetter });
    //     return;
    // }

    async verifySmtpSettings(): Promise<void> {
        return;
    }

    // async verifySmtpSettings(options: SmtpTargetOptions): Promise<void> {
    //     console.log({ options });
    //     return;
    // }
}
