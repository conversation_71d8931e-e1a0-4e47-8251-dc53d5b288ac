import * as schoolSettingsData from './__data__/smtp-settings.data.json';
import { UserContext } from './user-session';

export const SCHOOL_WITH_SMTP_UUID = '3ad12371-ba27-4748-923b-cf003b8a26b1';
export const SCHOOL_WITHOUT_SMTP_UUID = '3ad12371-ba27-4748-923b-cf003b8a26b2';

export const SCHOOL_SETTINGS = schoolSettingsData.find((s) => s.schoolUuid === SCHOOL_WITH_SMTP_UUID);

export const SCHOOL_SETTINGS_RESPONSE = {
    useCustomSmtp: SCHOOL_SETTINGS.useCustomSmtp,
    host: SCHOOL_SETTINGS.host,
    port: SCHOOL_SETTINGS.port,
    user: SCHOOL_SETTINGS.user,
    password: SCHOOL_SETTINGS.password,
    email: SCHOOL_SETTINGS.email,
};

//   {
//     "schoolUuid": "3ad12371-ba27-4748-923b-cf003b8a26b1",
//     "useCustomSmtp": true,
//     "host": "school3/production",
//     "port": 25,
//     "user": "<EMAIL>",
//     "password": "password3",
//     "email": "<EMAIL>"
//   }

export const CUSTOM_SMTP = {
    host: SCHOOL_SETTINGS.host,
    port: SCHOOL_SETTINGS.port,
    user: SCHOOL_SETTINGS.user,
    password: SCHOOL_SETTINGS.password,
    email: SCHOOL_SETTINGS.email,
};

export const DEFAULT_SMTP = {
    host: 'host.test',
    port: '25',
    user: 'user/test',
    password: '12345',
    email: '<EMAIL>',
    name: 'Рассылка Skillspace',
};

export const EMPLOYEE_UUID_01 = '6fc112f6-e471-4d83-94c2-ff447f43d5e2';
export const EMPLOYEE_UUID_02 = '6fc112f6-e471-4d83-94c2-ff447f43d5e3';

export const EMPLOYEE_01: UserContext = {
    userId: EMPLOYEE_UUID_01,
    role: 'ROLE_EMPLOYEE' as const,
    actions: ['ACTION_SCHOOL_WHITE_LABEL_MODIFY'],
    schoolId: SCHOOL_WITH_SMTP_UUID,
    userName: 'Спиридон Никанорович',
    userEmail: '<EMAIL>',
    unionAuthKey: '24',
};

export const EMPLOYEE_02: UserContext = {
    userId: EMPLOYEE_UUID_02,
    role: 'ROLE_EMPLOYEE' as const,
    actions: ['ACTION_SCHOOL_WHITE_LABEL_MODIFY'],
    schoolId: SCHOOL_WITHOUT_SMTP_UUID,
    userName: 'Степан Никифорович',
    userEmail: '<EMAIL>',
    unionAuthKey: '27',
};
