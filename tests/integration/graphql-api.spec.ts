import { SCHOOL_SETTINGS_RESPONSE } from './test-constants';
import { employeeSession01, prisma } from './test-setup';

describe('Резолвер настроек SMTP', () => {
    it('Должен получить сохраненные настройки smtp', async () => {
        const result = await employeeSession01.getSmtpSettings();
        expect(result).toEqual({ smtpSettings: SCHOOL_SETTINGS_RESPONSE });
    });

    it.each([{ email: '<EMAIL>' }, { user: 'user' }, { host: 'host' }, { port: 777 }, { password: '9&9I6_~56^5' }])(
        'Должен отключить использование кастомных настроек smtp при изменении %s',
        async (dto) => {
            const before = await employeeSession01.getSmtpSettings();
            expect(before.smtpSettings.useCustomSmtp).toBe(true);

            const after = await employeeSession01.updateSmtpSettings(dto);
            expect(after.updateSmtpSettings.useCustomSmtp).toBe(false);
        },
    );

    it.each([{ email: '' }, { user: '' }, { host: '' }, { port: 0 }, { password: '' }])(
        'Должен принимать в качестве значения %s',
        async (dto) => {
            const before = await employeeSession01.getSmtpSettings();
            expect(before.smtpSettings.useCustomSmtp).toBe(true);

            const after = await employeeSession01.updateSmtpSettings(dto);
            expect(after.updateSmtpSettings).toMatchObject(dto);
        },
    );

    it.skip.each(['<EMAIL> ', ' <EMAIL> ', ' <EMAIL>', 'wrongEmail', '@ru', 'wrong@Email'])(
        'Должен отклонить невалидный адрес: %s ',
        async (email) => {
            const result = await employeeSession01.updateSmtpSettings({ email });
            expect(result).toEqual(
                expect.arrayContaining([expect.objectContaining({ message: 'Bad Request Exception' })]),
            );
        },
    );

    it('Должен создать настройки', async () => {
        const setInput = {
            useCustomSmtp: true,
            host: 'SCHOOL_SETTINGS.host',
            port: 888,
            user: 'SCHOOL_SETTINGS.user',
            password: 'SCHOOL_SETTINGS.password',
            email: '<EMAIL>',
        };
        await prisma.smtpSettings.deleteMany();

        const result = await employeeSession01.updateSmtpSettings(setInput);
        expect(result).toEqual({ updateSmtpSettings: { ...setInput, useCustomSmtp: false } });
    });

    it('Должен позволить включить валидные настройки после создания', async () => {
        const setInput = {
            useCustomSmtp: true,
            host: 'SCHOOL_SETTINGS.host',
            port: 888,
            user: 'SCHOOL_SETTINGS.user',
            password: 'SCHOOL_SETTINGS.password',
            email: '<EMAIL>',
        };
        await prisma.smtpSettings.deleteMany();

        const created = await employeeSession01.updateSmtpSettings(setInput);
        expect(created).toEqual({ updateSmtpSettings: { ...setInput, useCustomSmtp: false } });

        const updated = await employeeSession01.updateSmtpSettings({ useCustomSmtp: true });
        expect(updated).toEqual({ updateSmtpSettings: setInput });
    });

    it('Должен создать настройки с невалидными значениями', async () => {
        const setInput = {
            useCustomSmtp: true,
            host: 'ost',
            port: 0,
            user: 'looser',
            password: '',
            email: 'ya.ru',
        };
        await prisma.smtpSettings.deleteMany();

        const created = await employeeSession01.updateSmtpSettings(setInput);
        expect(created).toEqual({ updateSmtpSettings: { ...setInput, useCustomSmtp: false } });
    });

    it('Должен отклонить невалидные настройки при включении', async () => {
        const setInput = {
            useCustomSmtp: true,
            host: 'ost',
            port: 0,
            user: 'looser',
            password: '',
            email: 'ya.ru',
        };
        await prisma.smtpSettings.deleteMany();

        const created = await employeeSession01.updateSmtpSettings(setInput);
        expect(created).toEqual({ updateSmtpSettings: { ...setInput, useCustomSmtp: false } });

        const updated = await employeeSession01.updateSmtpSettings({ useCustomSmtp: true });
        expect(updated).toEqual([
            {
                message: expect.stringContaining('Ошибка валидации SMTP настроек'),
                path: ['updateSmtpSettings'],
            },
        ]);
    });
});
