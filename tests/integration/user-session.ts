import { INestApplication } from '@nestjs/common';
import { AuthService } from '@skillspace/lib';
import { DocumentNode, print } from 'graphql';
import gql from 'graphql-tag';
import { Server } from 'http';
import * as request from 'supertest';
import TestAgent from 'supertest/lib/agent';

import { UpdateSmtpSettingsInput } from '../../src/emails/presentation/inputs/update-smtp-settings.input';
import { SmtpSettingsOutput } from '../../src/emails/presentation/outputs/school-settings.output';

type JSONPrimitive = string | number | boolean | null;
type JSONArray = JSONValue[];
type JSONValue = JSONPrimitive | JSONObject | JSONArray;

export type JSONObject = { [member: string]: JSONValue };

export type UserContext = {
    actions: string[];
    role: 'ROLE_EMPLOYEE' | 'ROLE_STUDENT' | 'ROLE_GUEST';
    schoolId: string | undefined;
    userId: string | undefined;
    userName: string | undefined;
    userEmail: string | undefined;
    unionAuthKey: string | undefined;
};

export class GqlTestSession {
    private agent: TestAgent;

    constructor(
        private readonly server: Server,
        private readonly token: string,
    ) {
        this.agent = request(this.server);
    }

    private async gql<T>(query: DocumentNode, variables: Record<string, any> = {}): Promise<T> {
        const queryString = print(query);

        const response = await this.agent
            .post('/graphql')
            .set('Authorization', `Bearer ${this.token}`)
            .set('Content-Type', 'application/json')
            .send({ query: queryString, variables });

        if (response.body.errors) {
            return response.body.errors;
        }

        return response.body.data as T;
    }

    public async getSmtpSettings(): Promise<{ smtpSettings: SmtpSettingsOutput }> {
        const query = gql`
            query getNotificationsSettings {
                smtpSettings {
                    useCustomSmtp
                    host
                    port
                    user
                    password
                    email
                }
            }
        `;
        return this.gql<{ smtpSettings: SmtpSettingsOutput }>(query);
    }

    public async updateSmtpSettings(
        params: UpdateSmtpSettingsInput | JSONObject,
    ): Promise<{ updateSmtpSettings: SmtpSettingsOutput }> {
        const mutation = gql`
            mutation ($params: UpdateSmtpSettingsInput!) {
                updateSmtpSettings(params: $params) {
                    useCustomSmtp
                    host
                    port
                    user
                    password
                    email
                }
            }
        `;
        return this.gql<{ updateSmtpSettings: SmtpSettingsOutput }>(mutation, { params });
    }
}

export async function createGqlTestSession(app: INestApplication, context: UserContext): Promise<GqlTestSession> {
    const authService = app.get<AuthService>(AuthService);
    const token = await authService.signAuthorizationToken(context);

    // const userToken = await authService.signAuthorizationToken({
    //     userId: 'c3d0d257-855e-41c7-869e-ead6682da2a3',
    //     schoolId: 'fabdd321-1d19-4686-9d5b-d8e5d028c8b8',
    //     role: 'ROLE_EMPLOYEE' as const,
    //     actions: [],
    //     userName: 'Спиридон Никанорович',
    //     userEmail: '<EMAIL>',
    //     unionAuthKey: '24',
    // });
    // console.log({ userToken });
    const server: Server = app.getHttpServer();
    return new GqlTestSession(server, token);
}
