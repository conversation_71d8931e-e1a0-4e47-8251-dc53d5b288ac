import { EmailServiceSendEmailToManyContractNamespace } from '@skillspace/amqp-contracts';

import { ISmtpProvider } from '../../src/emails/domain/infrastructure/smtp-provider.interface';
import { SMTP_PROVIDER } from '../../src/emails/injects';
import { EmailConsumer } from '../../src/emails/presentation/email.consumer';
import { SCHOOL_WITH_SMTP_UUID } from './test-constants';
import { employeeSession01, prisma } from './test-setup';
import { app } from './test-setup';

const formatAmqMessage = (
    payload: EmailServiceSendEmailToManyContractNamespace.RequestPayload,
): EmailServiceSendEmailToManyContractNamespace.Message => ({
    requestUuid: '202b67c2-5aee-4e44-abb3-b66b0747ea64',
    timestamp: Date.now(),
    payload,
});

const customSenderInput = {
    fromName: 'MyCoolSchool',
    fromEmail: '<EMAIL>',
};

const customSenderExpected = {
    subject: 'Test letter',
    to: ['<EMAIL>'],
    from: '"MyCoolSchool" <<EMAIL>>',
    html: expect.any(String),
    locale: 'ru',
};

const customNameExpected = {
    subject: 'Test letter',
    to: ['<EMAIL>'],
    from: '"MyCoolSchool" <<EMAIL>>',
    html: expect.any(String),
    locale: 'ru',
};

const messagePayload = {
    recipients: ['<EMAIL>'],
    subject: 'Test letter',
    body: {},
    template: 'test_smtp',
} as EmailServiceSendEmailToManyContractNamespace.RequestPayload;

const messagePayloadWithCustomSender = {
    ...messagePayload,
    ...customSenderInput,
} as EmailServiceSendEmailToManyContractNamespace.RequestPayload;

const defaultOptions = {
    host: 'host.test',
    port: 25,
    secure: false,
    auth: { user: 'user/test', pass: '12345' },
    // email: '<EMAIL>',
    // name: 'Рассылка Skillspace',
};

const customOptions = {
    host: 'school3/production',
    port: 25,
    secure: false,
    auth: { user: '<EMAIL>', pass: 'password3' },
    // user: '<EMAIL>',
    // password: 'password3',
    // email: '<EMAIL>',
};

// const CUSTOM_SMTP = {
//     host: 'school3/production',
//     port: 25,
//     user: '<EMAIL>',
//     password: 'password3',
//     email: '<EMAIL>',
// };

describe('Send Newsletter Integration Test', () => {
    let smtpProvider: ISmtpProvider;
    let emailConsumer: EmailConsumer;

    let sendEmailSpy: jest.SpyInstance;

    beforeAll(async () => {
        smtpProvider = app.get<ISmtpProvider>(SMTP_PROVIDER);
        emailConsumer = app.get<EmailConsumer>(EmailConsumer);
    });

    afterAll(async () => {
        await prisma.smtpSettings.deleteMany();
    });

    beforeEach(async () => (sendEmailSpy = jest.spyOn(smtpProvider, 'sendEmail')));
    afterEach(async () => jest.clearAllMocks());

    it('Should send email with default settings', async () => {
        await emailConsumer.sendEmailToMany(formatAmqMessage(messagePayloadWithCustomSender));
        expect(sendEmailSpy).toHaveBeenCalledWith(defaultOptions, expect.objectContaining(customNameExpected));
    });

    it('Should send email with default settings and custom sender name', async () => {
        await emailConsumer.sendEmailToMany(formatAmqMessage(messagePayloadWithCustomSender));
        expect(sendEmailSpy).toHaveBeenCalledWith(defaultOptions, expect.objectContaining(customNameExpected));
    });

    it('Should save smtp settings and send email with custom settings and custom email if custom name and email were not provided', async () => {
        await employeeSession01.updateSmtpSettings({ useCustomSmtp: true });

        await emailConsumer.sendEmailToMany({
            requestUuid: '202b67c2-5aee-4e44-abb3-b66b0747ea64',
            timestamp: 1643723400,
            payload: {
                schoolUuid: SCHOOL_WITH_SMTP_UUID,
                ...messagePayload,
            },
        });
        expect(sendEmailSpy).toHaveBeenCalledWith(
            customOptions,
            expect.objectContaining({
                from: '<EMAIL>',
            }),
        );
    });

    it('Не должен заменять email при использовании дефолтных настроек, но можно заменить название школы', async () => {
        await employeeSession01.updateSmtpSettings({ useCustomSmtp: false });

        await emailConsumer.sendEmailToMany(
            formatAmqMessage({
                schoolUuid: SCHOOL_WITH_SMTP_UUID,
                ...messagePayloadWithCustomSender,
            }),
        );
        expect(sendEmailSpy).toHaveBeenCalledWith(defaultOptions, expect.objectContaining(customNameExpected));
    });

    it('Должен использовать кастомные настройки', async () => {
        await employeeSession01.updateSmtpSettings({ useCustomSmtp: true });

        await emailConsumer.sendEmailToMany(
            formatAmqMessage({
                schoolUuid: SCHOOL_WITH_SMTP_UUID,
                ...messagePayloadWithCustomSender,
            }),
        );
        expect(sendEmailSpy).toHaveBeenCalledWith(customOptions, expect.objectContaining(customSenderExpected));
    });

    it('Should save smtp settings and send email with default settings and custom sender name', async () => {
        await employeeSession01.updateSmtpSettings({ useCustomSmtp: false });

        await emailConsumer.sendEmailToMany(
            formatAmqMessage({
                schoolUuid: SCHOOL_WITH_SMTP_UUID,
                ...messagePayloadWithCustomSender,
            }),
        );
        expect(sendEmailSpy).toHaveBeenCalledWith(defaultOptions, expect.objectContaining(customNameExpected));
    });
});
